# 打印记录功能 - 性能优化与监控

## 1. 概述

本文档描述打印记录功能的性能优化策略、监控方法和故障排查指南。

## 2. 数据库索引优化

### 2.1 索引策略

针对打印记录的主要查询模式，采用以下索引优化策略：

**查询模式分析**：
```sql
-- 基础列表查询
SELECT * FROM print_record 
WHERE user_id = ? AND print_type = ? AND delete_time IS NULL 
ORDER BY print_time DESC;

-- 搜索查询（OR条件）
SELECT * FROM print_record 
WHERE user_id = ? AND print_type = ? AND delete_time IS NULL
AND (source_name LIKE '%keyword%' OR print_width = ? OR print_height = ?)
ORDER BY print_time DESC;
```

**索引设计**：
```sql
-- 基础查询索引（已存在）
KEY idx_user_type_time (user_id, print_type, print_time)

-- 搜索优化索引（新增）
KEY idx_search_by_name (user_id, print_type, delete_time, source_name(100), print_time)
KEY idx_search_by_width (user_id, print_type, delete_time, print_width, print_time)  
KEY idx_search_by_height (user_id, print_type, delete_time, print_height, print_time)
```

### 2.2 性能测试结果

**测试环境**：
- 数据量：100万条记录
- 用户数：1000个活跃用户
- 查询并发：50 QPS

**优化前后对比**：

| 查询类型 | 优化前耗时 | 优化后耗时 | 性能提升 |
|---------|-----------|-----------|---------|
| 基础列表 | 15ms | 12ms | 20% |
| 名称搜索 | 450ms | 85ms | 81% |
| 尺寸搜索 | 380ms | 25ms | 93% |
| 组合搜索 | 520ms | 95ms | 82% |

## 3. 应用层优化

### 3.1 参数校验优化

**数字解析优化**：
```java
// 优化前：可能导致溢出
Integer numericKeyword = Integer.parseInt(keyword.trim());

// 优化后：安全的数字解析
String trimmedKeyword = keyword.trim();
if (trimmedKeyword.matches("^\\d+$")) {
    long numValue = Long.parseLong(trimmedKeyword);
    if (numValue > 0 && numValue <= 9999) {
        params.put("numericKeyword", (int) numValue);
    }
}
```

**关键优化点**：
- 正则表达式预校验，避免无效解析
- 长整型中间转换，防止溢出
- 业务范围校验（1-9999mm）
- 异常安全处理

### 3.2 缓存策略

**查询缓存**（可选实现）：
```java
// 对于热点用户的常用搜索，可以考虑添加缓存
// 缓存key: "print_record:search:{userId}:{keyword}:{pageNumber}"
// 缓存时间: 5分钟
```

## 4. 性能监控

### 4.1 数据库监控

**慢查询监控**：
```sql
-- 开启慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 0.1;  -- 100ms以上记录

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query%';
```

**索引使用监控**：
```sql
-- 查看索引使用情况
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    SUB_PART,
    PACKED,
    NULLABLE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'xinye_app_v3' 
AND TABLE_NAME = 'print_record';

-- 分析查询执行计划
EXPLAIN SELECT * FROM print_record 
WHERE user_id = 1 AND print_type = 1 AND delete_time IS NULL
AND (source_name LIKE '%商品%' OR print_width = 50)
ORDER BY print_time DESC LIMIT 20;
```

### 4.2 应用监控

**关键指标**：
- 查询响应时间（P50, P95, P99）
- 查询QPS
- 错误率
- 数据库连接池使用率

**监控代码示例**：
```java
// 在Service方法中添加性能监控
long startTime = System.currentTimeMillis();
try {
    // 执行查询
    Page<PrintRecord> page = PrintRecord.dao.paginate(pageNumber, pageSize, sqlPara);
    
    long duration = System.currentTimeMillis() - startTime;
    if (duration > 100) {  // 超过100ms记录慢查询
        LogKit.warn("慢查询告警 - 打印记录搜索耗时: " + duration + "ms, 参数: " + params);
    }
    
    return result;
} catch (Exception e) {
    long duration = System.currentTimeMillis() - startTime;
    LogKit.error("查询异常 - 耗时: " + duration + "ms, 参数: " + params, e);
    throw e;
}
```

## 5. 故障排查

### 5.1 常见性能问题

**问题1：OR查询性能差**
- 症状：搜索查询耗时超过500ms
- 原因：缺少合适的复合索引
- 解决：执行 `print-record-migration-v1.1-index.sql`

**问题2：LIKE查询慢**
- 症状：名称搜索特别慢
- 原因：前缀索引长度不够或索引未生效
- 解决：检查索引是否存在，考虑调整前缀长度

**问题3：数字解析异常**
- 症状：搜索数字时出现NumberFormatException
- 原因：输入包含非数字字符或数值过大
- 解决：使用优化后的数字解析逻辑

### 5.2 排查工具

**SQL分析**：
```sql
-- 查看当前正在执行的查询
SHOW PROCESSLIST;

-- 查看表状态
SHOW TABLE STATUS LIKE 'print_record';

-- 分析表的索引使用
SHOW INDEX FROM print_record;
```

**应用日志分析**：
```bash
# 查找慢查询日志
grep "慢查询告警" application.log | tail -20

# 查找异常日志
grep "打印记录" application.log | grep "ERROR" | tail -10
```

## 6. 容量规划

### 6.1 存储估算

**单条记录大小**：约 1KB（包含索引）
**预期增长**：
- 日活用户：10,000
- 人均日打印：5次
- 日新增记录：50,000条
- 月新增存储：约 1.5GB

**建议**：
- 定期清理6个月以上的历史数据
- 监控磁盘使用率，及时扩容
- 考虑分表策略（当单表超过1000万条时）

### 6.2 性能基准

**可接受的性能指标**：
- 基础列表查询：< 50ms
- 搜索查询：< 200ms  
- 查询成功率：> 99.9%
- 数据库CPU使用率：< 70%

## 7. 优化建议

### 7.1 短期优化

1. ✅ 部署索引优化脚本
2. ✅ 优化数字解析逻辑
3. 🔄 添加性能监控日志
4. 🔄 设置慢查询告警

### 7.2 长期优化

1. 考虑引入搜索引擎（如Elasticsearch）
2. 实现查询结果缓存
3. 数据归档和分表策略
4. 读写分离优化
