package com.sandu.xinye.common.interceptor;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.kit.LogKit;
import com.jfinal.render.JsonRender;
import com.sandu.xinye.api.v2.printrecord.exception.PrintRecordException;
import com.sandu.xinye.common.kit.RetKit;

/**
 * 全局异常拦截器
 * 将系统中的异常统一转换为用户友好的响应
 */
public class GlobalExceptionInterceptor implements Interceptor {
    @Override
    public void intercept(Invocation inv) {
        try {
            inv.invoke();
        } catch (PrintRecordException e) {
            LogKit.warn("业务异常: " + e.getMessage(), e);
            inv.getController().render(new JsonRender(RetKit.fail(e.getUserFriendlyMessage())).forIE());
        } catch (IllegalArgumentException e) {
            LogKit.warn("参数异常: " + e.getMessage(), e);
            inv.getController().render(new JsonRender(RetKit.fail("参数错误"))).forIE();
        } catch (Exception e) {
            LogKit.error("系统异常: " + e.getMessage(), e);
            inv.getController().render(new JsonRender(RetKit.fail("系统繁忙，请稍后重试")).forIE());
        }
    }
}
