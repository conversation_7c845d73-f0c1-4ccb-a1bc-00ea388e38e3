package com.sandu.xinye.api.v2.printrecord.exception;

/**
 * 打印记录相关异常
 * 
 * <AUTHOR> Team
 */
public class PrintRecordException extends Exception {
    
    public enum ErrorType {
        VALIDATION_ERROR("参数验证失败"),
        DATABASE_ERROR("数据库操作失败"),
        CACHE_ERROR("缓存操作失败"),
        BUSINESS_ERROR("业务逻辑错误"),
        SYSTEM_ERROR("系统错误");
        
        private final String description;
        
        ErrorType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    private final ErrorType errorType;
    
    public PrintRecordException(ErrorType errorType, String message) {
        super(message);
        this.errorType = errorType;
    }
    
    public PrintRecordException(ErrorType errorType, String message, Throwable cause) {
        super(message, cause);
        this.errorType = errorType;
    }
    
    public ErrorType getErrorType() {
        return errorType;
    }
    
    /**
     * 获取用户友好的错误消息
     */
    public String getUserFriendlyMessage() {
        switch (errorType) {
            case VALIDATION_ERROR:
                return getMessage(); // 参数错误可以直接返回
            case DATABASE_ERROR:
            case CACHE_ERROR:
            case SYSTEM_ERROR:
                return "系统繁忙，请稍后重试";
            case BUSINESS_ERROR:
                return getMessage(); // 业务错误可以返回具体信息
            default:
                return "操作失败，请稍后重试";
        }
    }
}
