package com.sandu.xinye.common.model;

import com.sandu.xinye.common.model.base.BaseUserPrintSetting;

/**
 * 用户打印记录设置模型
 * 
 * <AUTHOR> Team
 */
@SuppressWarnings("serial")
public class UserPrintSetting extends BaseUserPrintSetting<UserPrintSetting> {
	public static final UserPrintSetting dao = new UserPrintSetting().dao();
	
	/**
	 * 记录开关状态常量
	 */
	public static final boolean RECORD_ENABLED = true;   // 开启记录
	public static final boolean RECORD_DISABLED = false; // 关闭记录
	
	/**
	 * 判断是否开启记录
	 * @return true-开启记录，false-关闭记录
	 */
	public boolean isRecordEnabled() {
		Boolean enabled = getRecordEnabled();
		return enabled != null ? enabled : RECORD_ENABLED; // 默认开启
	}
	
	/**
	 * 根据用户ID查找设置
	 * @param userId 用户ID
	 * @return 用户打印设置，如果不存在返回null
	 */
	public static UserPrintSetting findByUserId(Integer userId) {
		if (userId == null) {
			return null;
		}
		return dao.findFirst("SELECT * FROM user_print_setting WHERE user_id = ?", userId);
	}
	
	/**
	 * 获取用户的记录开关状态
	 * @param userId 用户ID
	 * @return true-开启记录，false-关闭记录，默认开启
	 */
	public static boolean isUserRecordEnabled(Integer userId) {
		if (userId == null) {
			return RECORD_ENABLED; // 默认开启
		}
		
		UserPrintSetting setting = findByUserId(userId);
		if (setting == null) {
			return RECORD_ENABLED; // 无设置记录时默认开启
		}
		
		return setting.isRecordEnabled();
	}
}
