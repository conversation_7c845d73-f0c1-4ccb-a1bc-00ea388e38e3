-- XPrinter打印记录功能 - 索引优化迁移脚本 V1.1
-- 创建时间: 2024-08-27
-- 说明: 为打印记录搜索功能添加性能优化索引

USE xinye_app_v3;

-- =====================================================
-- 添加搜索优化索引
-- =====================================================

-- 1. 删除原有的简单source_name索引，替换为复合索引
DROP INDEX IF EXISTS idx_source_name ON print_record;

-- 2. 为名称搜索创建优化索引
CREATE INDEX idx_search_by_name ON print_record (user_id, print_type, delete_time, source_name(100), print_time);

-- 3. 为宽度搜索创建优化索引
CREATE INDEX idx_search_by_width ON print_record (user_id, print_type, delete_time, print_width, print_time);

-- 4. 为高度搜索创建优化索引
CREATE INDEX idx_search_by_height ON print_record (user_id, print_type, delete_time, print_height, print_time);

-- =====================================================
-- 验证索引创建结果
-- =====================================================

-- 查看表的所有索引
SHOW INDEX FROM print_record;

-- =====================================================
-- 回滚脚本（如需要）
-- =====================================================

/*
-- 如果需要回滚到原来的索引结构，执行以下语句：

DROP INDEX IF EXISTS idx_search_by_name ON print_record;
DROP INDEX IF EXISTS idx_search_by_width ON print_record;  
DROP INDEX IF EXISTS idx_search_by_height ON print_record;

-- 恢复原来的简单索引
CREATE INDEX idx_source_name ON print_record (source_name(100));
*/
