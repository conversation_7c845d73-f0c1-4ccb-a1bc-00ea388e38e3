package com.sandu.xinye.api.v2.printrecord;

import com.jfinal.aop.Before;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.api.v2.printrecord.dto.SaveTemplatePrintRecordRequest;
import com.sandu.xinye.api.v2.printrecord.dto.SetRecordSwitchRequest;
import com.sandu.xinye.common.annotation.JsonBody;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;

/**
 * 打印记录控制器
 * 
 * <AUTHOR> Team
 */
public class PrintRecordController extends AppController {

    /**
     * 获取模板打印记录列表
     * 
     * @description 获取用户的模板打印记录，支持分页，默认100条
     * @param pageNumber 页码，默认1
     * @param pageSize 页大小，默认100
     */
    public void getTemplateList() {
        User user = getUser();
        if (user == null) {
            renderJson(RetKit.fail("用户未登录"));
            return;
        }
        Integer userId = user.getUserId();

        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 100);

        RetKit ret = PrintRecordService.me.getTemplatePrintRecordList(userId, pageNumber, pageSize);
        renderJson(ret);
    }

    /**
     * 搜索模板打印记录
     *
     * @description 按关键词搜索模板打印记录（匹配名称、宽度或高度）
     * @param keyword 搜索关键词（匹配模板名称、打印宽度或打印高度）
     * @param pageNumber 页码，默认1
     * @param pageSize 页大小，默认20
     */
    public void searchTemplate() {
        User user = getUser();
        if (user == null) {
            renderJson(RetKit.fail("用户未登录"));
            return;
        }
        Integer userId = user.getUserId();

        String keyword = getPara("keyword");
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 20);

        RetKit ret = PrintRecordService.me.searchTemplatePrintRecord(userId, keyword, pageNumber, pageSize);
        renderJson(ret);
    }

    /**
     * 删除打印记录
     * 
     * @description 软删除指定的打印记录
     * @param id 打印记录ID
     */
    public void delete() {
        User user = getUser();
        if (user == null) {
            renderJson(RetKit.fail("用户未登录"));
            return;
        }
        Integer userId = user.getUserId();

        Long recordId = getParaToLong("id");
        if (recordId == null) {
            renderJson(RetKit.fail("记录ID不能为空"));
            return;
        }

        RetKit ret = PrintRecordService.me.deletePrintRecord(userId, recordId);
        renderJson(ret);
    }

    /**
     * 获取打印记录详情
     * 
     * @description 获取单条打印记录的详细信息
     * @param id 打印记录ID（路径参数）
     */
    public void detail() {
        User user = getUser();
        if (user == null) {
            renderJson(RetKit.fail("用户未登录"));
            return;
        }
        Integer userId = user.getUserId();

        String idStr = getPara(0); // 获取路径参数
        if (StrKit.isBlank(idStr)) {
            renderJson(RetKit.fail("记录ID不能为空"));
            return;
        }

        Long recordId;
        try {
            recordId = Long.parseLong(idStr);
        } catch (NumberFormatException e) {
            renderJson(RetKit.fail("记录ID格式错误"));
            return;
        }

        RetKit ret = PrintRecordService.me.getPrintRecordDetail(userId, recordId);
        renderJson(ret);
    }

    /**
     * 保存模板打印记录
     *
     * @description 模板打印时保存打印记录，使用POST方法和JSON body传参
     * @param request 保存模板打印记录请求参数
     */
    @Before({PostOnlyInterceptor.class})
    public void saveTemplate(@JsonBody SaveTemplatePrintRecordRequest request) {
        User user = getUser();
        if (user == null) {
            renderJson(RetKit.fail("用户未登录"));
            return;
        }
        Integer userId = user.getUserId();

        // 参数验证
        if (request.getTemplateId() == null) {
            renderJson(RetKit.fail("模板ID不能为空"));
            return;
        }
        if (StrKit.isBlank(request.getTemplateName())) {
            renderJson(RetKit.fail("模板名称不能为空"));
            return;
        }
        if (request.getPrintPlatform() == null) {
            renderJson(RetKit.fail("打印端不能为空"));
            return;
        }

        // 设置默认值
        Integer printCopies = request.getPrintCopies() != null ? request.getPrintCopies() : 1;

        RetKit ret = PrintRecordService.me.saveTemplatePrintRecord(userId, request.getTemplateId(),
                request.getTemplateName(), request.getTemplateCover(), request.getPrintWidth(),
                request.getPrintHeight(), printCopies, request.getPrintPlatform(), request.getTemplateData());
        renderJson(ret);
    }

    /**
     * 获取文档打印记录列表（预留接口）
     */
    public void getDocumentList() {
        renderJson(RetKit.fail("文档打印记录功能暂未开放"));
    }

    /**
     * 获取图片打印记录列表（预留接口）
     */
    public void getImageList() {
        renderJson(RetKit.fail("图片打印记录功能暂未开放"));
    }

    /**
     * 搜索文档打印记录（预留接口）
     */
    public void searchDocument() {
        renderJson(RetKit.fail("文档打印记录功能暂未开放"));
    }

    /**
     * 搜索图片打印记录（预留接口）
     */
    public void searchImage() {
        renderJson(RetKit.fail("图片打印记录功能暂未开放"));
    }

    /**
     * 获取用户打印记录开关状态
     *
     * @description 查询当前用户是否开启打印记录功能
     */
    public void recordSwitch() {
        User user = getUser();
        if (user == null) {
            renderJson(RetKit.fail("用户未登录"));
            return;
        }

        RetKit ret = UserPrintSettingService.me.getUserPrintSetting(user.getUserId());
        renderJson(ret);
    }

    /**
     * 设置用户打印记录开关
     *
     * @description 设置当前用户的打印记录开关，关闭时会清空历史记录
     * @param request 设置请求参数
     */
    @Before({PostOnlyInterceptor.class})
    public void setRecordSwitch(@JsonBody SetRecordSwitchRequest request) {
        User user = getUser();
        if (user == null) {
            renderJson(RetKit.fail("用户未登录"));
            return;
        }

        // 参数验证
        if (request.getEnabled() == null) {
            renderJson(RetKit.fail("enabled参数不能为空"));
            return;
        }

        RetKit ret = UserPrintSettingService.me.setRecordEnabled(user.getUserId(), request.getEnabled());
        renderJson(ret);
    }
}
