-- 用户打印记录设置表迁移脚本
-- 版本: v1.0
-- 创建时间: 2025-08-27
-- 说明: 新增用户打印记录开关设置功能

-- 创建用户打印记录设置表
CREATE TABLE `user_print_setting` (
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `record_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否记录打印记录：1-开启，0-关闭',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`),
  KEY `idx_record_enabled` (`record_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户打印记录设置';

-- 说明：
-- 1. user_id 为主键，确保每个用户只有一条设置记录
-- 2. record_enabled 默认为1（开启），向后兼容现有用户
-- 3. 若用户无设置记录，应用层视为开启状态
-- 4. updated_at 自动更新时间戳，便于追踪设置变更
-- 5. 添加 record_enabled 索引，便于统计分析
