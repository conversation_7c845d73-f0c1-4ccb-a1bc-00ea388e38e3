package com.sandu.xinye.api.v2.ocr;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import com.sandu.xinye.api.v2.ocr.util.CoordinateUtils;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.BinaryBitmap;
import com.google.zxing.DecodeHintType;
import com.google.zxing.MultiFormatReader;
import com.google.zxing.Result;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.common.GlobalHistogramBinarizer;
import com.google.zxing.common.HybridBinarizer;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 元素转换器
 * 将TextIn API的识别结果转换为简化的中间态数据格式
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public class ElementConverter {

    public static final ElementConverter me = new ElementConverter();

    /**
     * 转换TextIn识别结果为简化中间态格式
     * 新架构：优先使用detail数组进行元素分类，再结合structured和content数组获取详细信息
     *
     * @param textInResponse TextIn API响应
     * @param imageFile 原始图片文件
     * @param imageWidth 图片宽度
     * @param imageHeight 图片高度
     * @return OcrResponse 转换后的响应数据
     */
    public OcrResponse convertToXPrinterFormat(TextInResponse textInResponse,
                                              File imageFile,
                                              int imageWidth,
                                              int imageHeight) {
        LogKit.info("开始转换TextIn识别结果为简化中间态格式（新架构：detail优先）");

        OcrResponse response = new OcrResponse();

        // 设置图片信息
        String format = getImageFormat(imageFile.getName());
        response.setImageInfo(imageWidth, imageHeight, format);

        // 保存原图句柄为实例字段，供本地裁剪兜底使用
        this.currentImageFile = imageFile;

        // 收集TextIn识别到但没有内容的条码位置
        List<PendingBarcode> pendingBarcodes = new ArrayList<>();

        if (textInResponse.getResult() != null) {
            // 获取detail数组（高级语义信息）和页面信息
            List<Map<String, Object>> detailList = textInResponse.getDetail();
            List<Map<String, Object>> pages = textInResponse.getPages();

            LogKit.info("TextIn返回数据概况: detail数量=" + (detailList != null ? detailList.size() : 0) +
                       ", 页面数量=" + (pages != null ? pages.size() : 0));

            if (pages != null && !pages.isEmpty()) {
                // 保存页面数据到实例变量，供后续方法使用
                Map<String, Object> firstPage = pages.get(0);
                this.currentPageData = firstPage;
                this.currentContentList = (List<Map<String, Object>>) firstPage.get("content");
                this.currentStructuredList = (List<Map<String, Object>>) firstPage.get("structured");

                LogKit.info("页面数据概况: content数量=" + (currentContentList != null ? currentContentList.size() : 0) +
                           ", structured数量=" + (currentStructuredList != null ? currentStructuredList.size() : 0));

                // 【新架构】优先使用detail数组进行元素分类和处理
                if (detailList != null && !detailList.isEmpty()) {
                    convertDetailElements(detailList, response);
                } else {
                    LogKit.warn("detail数组为空，降级使用传统方法");
                    // 降级处理：使用原有方法
                    convertRawOcrElements(firstPage, response);
                    convertStructuredElements(firstPage, response);
                }

                // 收集需要ZXing补充的条码位置
                collectPendingBarcodes(response, pendingBarcodes);
            } else {
                LogKit.warn("TextIn未返回任何页面数据");
            }

            // 仅对TextIn识别到但没有内容的条码使用ZXing补充
            if (!pendingBarcodes.isEmpty()) {
                LogKit.info("发现 " + pendingBarcodes.size() + " 个需要ZXing补充内容的条码");
                detectBarcodesWithZXing(imageFile, response, pendingBarcodes);
            } else {
                LogKit.info("没有需要ZXing补充的条码");
            }
        }

        // 【新增】进行条形码与文本的合并处理
        try {
            LogKit.info("开始进行条形码与文本合并处理，当前元素数量: " + (response.getElements() != null ? response.getElements().size() : "null"));
            mergeBarcodeWithText(response, textInResponse);
        } catch (Exception e) {
            LogKit.error("条形码文本合并过程中出现异常: " + e.getMessage(), e);
        }

        // 【新增】智能识别统计日志
        logSmartDetectionStatistics(response);

        LogKit.info("转换完成，共识别到 " + response.getElements().size() + " 个元素");
        return response;
    }

    /**
     * 【新架构核心方法】基于detail数组进行元素分类和转换
     * detail数组提供了高级语义信息，是TextIn官方推荐的主要数据处理入口
     *
     * @param detailList detail数组，包含所有元素的类型和语义信息
     * @param response OCR响应对象
     */
    @SuppressWarnings("unchecked")
    private void convertDetailElements(List<Map<String, Object>> detailList, OcrResponse response) {
        LogKit.info("【新架构】开始基于detail数组处理元素，数量: " + detailList.size());

        for (Map<String, Object> detailItem : detailList) {
            try {
                String type = (String) detailItem.get("type");
                String subType = (String) detailItem.get("sub_type");
                Integer contentIndex = null;
                Object contentObj = detailItem.get("content");
                if (contentObj instanceof Number) {
                    contentIndex = ((Number) contentObj).intValue();
                }

                LogKit.info("处理detail元素: type=" + type + ", sub_type=" + subType + ", content_index=" + contentIndex);

                // 根据detail的类型进行分类处理
                switch (type) {
                    case "paragraph":
                        convertDetailParagraph(detailItem, response);
                        break;
                    case "image":
                        convertDetailImage(detailItem, response);
                        break;
                    case "table":
                        convertDetailTable(detailItem, response);
                        break;
                    default:
                        LogKit.info("跳过未知的detail类型: " + type);
                        break;
                }
            } catch (Exception e) {
                LogKit.error("处理detail元素时出错: " + e.getMessage(), e);
            }
        }

        // 处理structured数组中可能的额外表格信息
        processAdditionalStructuredElements(response);

        LogKit.info("【新架构】detail元素处理完成");
    }

    /**
     * 处理detail中的paragraph类型元素（文本、标题等）
     */
    @SuppressWarnings("unchecked")
    private void convertDetailParagraph(Map<String, Object> detailItem, OcrResponse response) {
        try {
            String subType = (String) detailItem.get("sub_type");
            Integer contentIndex = null;
            Object contentObj = detailItem.get("content");
            if (contentObj instanceof Number) {
                contentIndex = ((Number) contentObj).intValue();
            }
            String rawText = (String) detailItem.get("text");
            List<Integer> position = (List<Integer>) detailItem.get("position");

            // 清理markdown格式符号
            String text = cleanMarkdownFormatting(rawText);

            // 验证必要字段
            if (contentIndex == null || currentContentList == null ||
                contentIndex < 0 || contentIndex >= currentContentList.size()) {
                LogKit.warn("无效的content索引: " + contentIndex);
                return;
            }

            if (position == null || position.size() < 8) {
                LogKit.warn("无效的位置信息");
                return;
            }

            if (StrKit.isBlank(text)) {
                LogKit.info("跳过空文本元素");
                return;
            }

            // 获取对应的content元素获取更多信息
            Map<String, Object> contentItem = currentContentList.get(contentIndex);

            // 计算位置信息，处理可能的Double类型
            int minX = Math.min(Math.min(toInt(position.get(0)), toInt(position.get(2))),
                               Math.min(toInt(position.get(4)), toInt(position.get(6))));
            int minY = Math.min(Math.min(toInt(position.get(1)), toInt(position.get(3))),
                               Math.min(toInt(position.get(5)), toInt(position.get(7))));
            int maxX = Math.max(Math.max(toInt(position.get(0)), toInt(position.get(2))),
                               Math.max(toInt(position.get(4)), toInt(position.get(6))));
            int maxY = Math.max(Math.max(toInt(position.get(1)), toInt(position.get(3))),
                               Math.max(toInt(position.get(5)), toInt(position.get(7))));
            int width = maxX - minX;
            int height = maxY - minY;

            // 根据sub_type判断样式
            boolean isBold = isTextBoldBySubType(subType);
            boolean isItalic = extractItalicFromContent(contentItem);

            // 计算字符宽度
            double charWidth = calculateCharWidthFromContent(contentItem, text, width);

            // 提取其他属性
            double rotationAngle = extractRotationAngle(contentItem);
            String textDirection = extractTextDirection(contentItem);
            double confidence = extractConfidence(contentItem);
            boolean isHandwritten = extractHandwritten(contentItem);

            LogKit.info("添加文本元素[detail]: '" + text + "', 位置=[" + minX + "," + minY + "," + width + "," + height + "], 样式: bold=" + isBold + ", italic=" + isItalic);

            response.addTextElement(minX, minY, width, height, text, isBold, isItalic, charWidth,
                                  rotationAngle, textDirection, confidence, isHandwritten);

        } catch (Exception e) {
            LogKit.error("处理paragraph元素时出错: " + e.getMessage(), e);
        }
    }

    /**
     * 处理detail中的image类型元素（二维码、条形码等）
     */
    @SuppressWarnings("unchecked")
    private void convertDetailImage(Map<String, Object> detailItem, OcrResponse response) {
        try {
            String subType = (String) detailItem.get("sub_type");
            List<Integer> position = (List<Integer>) detailItem.get("position");

            if (position == null || position.size() < 8) {
                LogKit.warn("图像元素位置信息无效");
                return;
            }

            // 计算位置信息（处理Double到Integer的转换）
            int minX = Math.min(Math.min(toInt(position.get(0)), toInt(position.get(2))),
                               Math.min(toInt(position.get(4)), toInt(position.get(6))));
            int minY = Math.min(Math.min(toInt(position.get(1)), toInt(position.get(3))),
                               Math.min(toInt(position.get(5)), toInt(position.get(7))));
            int maxX = Math.max(Math.max(toInt(position.get(0)), toInt(position.get(2))),
                               Math.max(toInt(position.get(4)), toInt(position.get(6))));
            int maxY = Math.max(Math.max(toInt(position.get(1)), toInt(position.get(3))),
                               Math.max(toInt(position.get(5)), toInt(position.get(7))));
            int width = maxX - minX;
            int height = maxY - minY;

            LogKit.info("处理图像元素: sub_type=" + subType + ", 位置=[" + minX + "," + minY + "," + width + "," + height + "]");

            // 根据sub_type处理不同类型的图像，如果sub_type为空则先尝试智能识别
            if (StrKit.isBlank(subType)) {
                // 没有sub_type的图像，先尝试智能识别是否为条码
                String imageUrl = extractImageUrl(detailItem);
                LogKit.info("发现没有sub_type的图像，尝试智能识别: 位置=[" + minX + "," + minY + "," + width + "," + height + "], imageUrl=" + imageUrl);

                // 【新增】尝试智能识别是否为二维码或条形码
                BarcodeDetectionResult detectionResult = tryDetectBarcodeFromImage(imageUrl, minX, minY, width, height);

                if (detectionResult.isDetected()) {
                    // 识别成功，添加相应的条码元素
                    if ("qrcode".equals(detectionResult.type)) {
                        LogKit.info("✅ 智能识别为二维码，内容: " + detectionResult.content);
                        response.addQrCodeElement(minX, minY, width, height, detectionResult.content);
                    } else if ("barcode".equals(detectionResult.type)) {
                        LogKit.info("✅ 智能识别为条形码，内容: " + detectionResult.content);
                        response.addBarcodeElement(minX, minY, width, height, detectionResult.content, "CODE_128");
                    }

                    // 保存图片URL到元素中（用于调试和验证）
                    if (!StrKit.isBlank(imageUrl) && !response.getElements().isEmpty()) {
                        Map<String, Object> lastElement = response.getElements().get(response.getElements().size() - 1);
                        lastElement.put("imageUrl", imageUrl);
                        lastElement.put("detectionMethod", detectionResult.method);
                    }
                } else {
                    // 智能识别失败，尝试使用原图按坐标+padding本地裁剪兜底（解决 TextIn 子图被截断导致解码失败的问题）
                    String localCropDecoded = null;
                    try {
                        localCropDecoded = detectBarcodeByLocalCrop(minX, minY, width, height);
                    } catch (Exception ex) {
                        LogKit.debug("本地裁剪兜底异常: " + ex.getMessage());
                    }
                    if (!StrKit.isBlank(localCropDecoded)) {
                        LogKit.info("✅ 本地裁剪兜底识别成功，内容: " + localCropDecoded);
                        // 根据内容类型简单判断：优先尝试按二维码处理（多数场景），如需更精细可增加模式判断
                        response.addQrCodeElement(minX, minY, width, height, localCropDecoded);
                        // 为调试记录来源
                        if (!response.getElements().isEmpty()) {
                            Map<String, Object> last = response.getElements().get(response.getElements().size() - 1);
                            last.put("detectionMethod", "local-crop");
                        }
                    } else {
                        // 仍无法识别，作为普通图片处理
                        LogKit.info("智能识别与本地裁剪兜底均失败，作为普通图片处理: 位置=[" + minX + "," + minY + "," + width + "," + height + "], imageUrl=" + imageUrl);
                        response.addImageElement(minX, minY, width, height, imageUrl, "generic", 0.0, true);
                    }
                }
            } else {
                switch (subType) {
                case "qrcode":
                    // 对于二维码，尝试从content中查找内容
                    String qrContent = findQrCodeContentForDetail(detailItem);
                    String qrImageUrl = (String) detailItem.get("image_url");  // 获取TextIn返回的二维码图片URL

                    if (!StrKit.isBlank(qrContent)) {
                        LogKit.info("添加二维码元素[detail]: 内容=" + qrContent);
                        response.addQrCodeElement(minX, minY, width, height, qrContent);
                    } else {
                        // 即使内容为空也要添加二维码元素，后续由ZXing补充
                        LogKit.info("添加二维码元素[detail]: 内容为空，将由ZXing补充" +
                                   (qrImageUrl != null ? "，imageUrl=" + qrImageUrl : ""));
                        response.addQrCodeElement(minX, minY, width, height, "");  // 添加空内容的二维码
                    }

                    // 如果有图片URL，将其保存到元素中（用于后续ZXing识别）
                    if (!StrKit.isBlank(qrImageUrl) && !response.getElements().isEmpty()) {
                        Map<String, Object> lastElement = response.getElements().get(response.getElements().size() - 1);
                        lastElement.put("imageUrl", qrImageUrl);
                    }
                    break;
                case "barcode":
                    // 对于条形码，尝试从content中查找内容
                    String barcodeContent = findBarcodeContentForDetail(detailItem);
                    String barcodeImageUrl = (String) detailItem.get("image_url");  // 获取TextIn返回的条形码图片URL

                    if (!StrKit.isBlank(barcodeContent)) {
                        LogKit.info("添加条形码元素[detail]: 内容=" + barcodeContent);
                        response.addBarcodeElement(minX, minY, width, height, barcodeContent, "CODE_128");
                    } else {
                        // 即使内容为空也要添加条形码元素，后续由ZXing补充
                        LogKit.info("添加条形码元素[detail]: 内容为空，将由ZXing补充" +
                                   (barcodeImageUrl != null ? "，imageUrl=" + barcodeImageUrl : ""));
                        response.addBarcodeElement(minX, minY, width, height, "", "CODE_128");  // 添加空内容的条形码
                    }

                    // 如果有图片URL，将其保存到元素中（用于后续ZXing识别）
                    if (!StrKit.isBlank(barcodeImageUrl) && !response.getElements().isEmpty()) {
                        Map<String, Object> lastElement = response.getElements().get(response.getElements().size() - 1);
                        lastElement.put("imageUrl", barcodeImageUrl);
                    }
                    break;
                case "chart":
                case "stamp":
                case "logo":
                case "icon":
                case "graphic":
                    // 处理图片元素
                    String imageUrl = extractImageUrl(detailItem);
                    String imageType = mapImageSubType(subType);
                    double confidence = extractImageConfidence(detailItem);

                    if (!StrKit.isBlank(imageUrl)) {
                        LogKit.info("添加图片元素[detail]: " + imageType + " - " + imageUrl);
                        response.addImageElement(minX, minY, width, height, imageUrl, imageType, confidence, true);
                    } else {
                        LogKit.info("图片元素未找到有效的imageUrl，类型: " + subType);
                    }
                    break;
                default:
                    // 对于所有其他未知的sub_type，先尝试智能识别是否为条码
                    LogKit.info("发现未知图像sub_type: " + subType + "，尝试智能识别是否为条码");
                    String unknownImageUrl = extractImageUrl(detailItem);
                    double unknownConfidence = extractImageConfidence(detailItem);

                    // 【新增】尝试智能识别是否为二维码或条形码
                    BarcodeDetectionResult detectionResult = tryDetectBarcodeFromImage(unknownImageUrl, minX, minY, width, height);

                    if (detectionResult.isDetected()) {
                        // 识别成功，添加相应的条码元素
                        if ("qrcode".equals(detectionResult.type)) {
                            LogKit.info("✅ 未知sub_type智能识别为二维码，内容: " + detectionResult.content);
                            response.addQrCodeElement(minX, minY, width, height, detectionResult.content);
                        } else if ("barcode".equals(detectionResult.type)) {
                            LogKit.info("✅ 未知sub_type智能识别为条形码，内容: " + detectionResult.content);
                            response.addBarcodeElement(minX, minY, width, height, detectionResult.content, "CODE_128");
                        }

                        // 保存图片URL和原始sub_type到元素中（用于调试和验证）
                        if (!StrKit.isBlank(unknownImageUrl) && !response.getElements().isEmpty()) {
                            Map<String, Object> lastElement = response.getElements().get(response.getElements().size() - 1);
                            lastElement.put("imageUrl", unknownImageUrl);
                            lastElement.put("detectionMethod", detectionResult.method);
                            lastElement.put("originalSubType", subType);
                        }
                    } else {
                        // 智能识别失败，确实是普通图片
                        String unknownImageType = mapImageSubType(subType); // 会返回"generic"

                        if (!StrKit.isBlank(unknownImageUrl)) {
                            LogKit.info("未知sub_type智能识别失败，作为通用图片处理[detail]: " + unknownImageType + " - " + unknownImageUrl);
                            response.addImageElement(minX, minY, width, height, unknownImageUrl, unknownImageType, unknownConfidence, true);
                        } else {
                            LogKit.info("通用图片元素未找到有效的imageUrl，类型: " + subType);
                        }
                    }
                    break;
                }
            }

        } catch (Exception e) {
            LogKit.error("处理image元素时出错: " + e.getMessage(), e);
        }
    }

    /**
     * 处理detail中的table类型元素
     */
    @SuppressWarnings("unchecked")
    private void convertDetailTable(Map<String, Object> detailItem, OcrResponse response) {
        try {
            List<Integer> position = (List<Integer>) detailItem.get("position");
            List<Map<String, Object>> cells = (List<Map<String, Object>>) detailItem.get("cells");

            if (position == null || position.size() < 8) {
                LogKit.warn("表格元素位置信息无效");
                return;
            }

            // 计算位置信息
            int minX = Math.min(Math.min(position.get(0), position.get(2)),
                               Math.min(position.get(4), position.get(6)));
            int minY = Math.min(Math.min(position.get(1), position.get(3)),
                               Math.min(position.get(5), position.get(7)));
            int maxX = Math.max(Math.max(position.get(0), position.get(2)),
                               Math.max(position.get(4), position.get(6)));
            int maxY = Math.max(Math.max(position.get(1), position.get(3)),
                               Math.max(position.get(5), position.get(7)));
            int width = maxX - minX;
            int height = maxY - minY;

            if (cells != null && !cells.isEmpty()) {
                // 解析表格结构
                TableStructure tableStructure = parseDetailTableCells(cells);

                // 判断是否为边框（1行1列且内容为空）
                if (isTableActuallyBorder(tableStructure)) {
                    LogKit.info("识别为边框元素[detail]: 位置=[" + minX + "," + minY + "," + width + "," + height + "]");
                    response.addBorderElement(minX, minY, width, height);
                    return;
                }

                // 从structured数组中获取行高列宽信息
                TableDimensions dimensions = extractTableDimensions();

                LogKit.info("添加表格元素[detail]: " + tableStructure.rows + "行 x " + tableStructure.cols + "列, " +
                           tableStructure.cells.size() + "个单元格");

                // 【新增】检测表格单元格内的条码并生成独立组件
                detectBarcodesInTableCells(cells, minX, minY, response);

                if (dimensions != null) {
                    LogKit.info("表格尺寸信息: 行高=" + dimensions.rowHeights + ", 列宽=" + dimensions.columnWidths);
                    response.addTableElement(minX, minY, width, height,
                                            tableStructure.rows, tableStructure.cols, tableStructure.cells,
                                            dimensions.rowHeights, dimensions.columnWidths);
                } else {
                    LogKit.warn("未找到表格尺寸信息，使用默认方法");
                    response.addTableElement(minX, minY, width, height,
                                            tableStructure.rows, tableStructure.cols, tableStructure.cells);
                }
            } else {
                // 没有cells数据，判定为边框
                LogKit.info("表格没有cells数据，识别为边框元素");
                response.addBorderElement(minX, minY, width, height);
            }

        } catch (Exception e) {
            LogKit.error("处理table元素时出错: " + e.getMessage(), e);
        }
    }

    /**
     * 转换原始OCR结果
     * 基于TextIn API的实际响应格式
     */
    @SuppressWarnings("unchecked")
    private void convertRawOcrElements(Map<String, Object> pageData, OcrResponse response) {
        // 获取content数组 (TextIn实际使用的字段名)
        List<Map<String, Object>> contentList = (List<Map<String, Object>>) pageData.get("content");
        LogKit.info("从pageData获取content，结果: " + (contentList != null ? "存在，数量=" + contentList.size() : "null"));
        if (contentList == null || contentList.isEmpty()) {
            LogKit.warn("content为空，无法转换OCR元素");
            return;
        }

        // 获取structured数组，用于提取sub_type等结构化信息
        List<Map<String, Object>> structuredList = (List<Map<String, Object>>) pageData.get("structured");

        // 获取被structured元素使用的content索引，避免重复处理
        Set<Integer> usedContentIndices = getContentIndicesUsedByStructured(structuredList);
        LogKit.info("structured元素使用的content索引: " + usedContentIndices.size() + "个");
        LogKit.info("从pageData获取structured，结果: " + (structuredList != null ? "存在，数量=" + structuredList.size() : "null"));

        // 建立content元素ID到structured信息的映射
        Map<Integer, Map<String, Object>> structuredMap = buildContentToStructuredMapping(contentList, structuredList);

        LogKit.info("转换原始OCR元素，数量: " + contentList.size());

        // 【调试增强】预先分析content中的图像元素
        analyzeImageElementsInContent(contentList);

        for (Map<String, Object> ocrItem : contentList) {
            String text = (String) ocrItem.get("text");
            String type = (String) ocrItem.get("type");
            List<Integer> pos = (List<Integer>) ocrItem.get("pos");

            // 获取对应的structured信息
            Integer itemId = (Integer) ocrItem.get("id");

            // 跳过已被structured元素使用的content，避免重复处理
            if (usedContentIndices.contains(itemId)) {
                LogKit.info("跳过被structured元素使用的content[" + itemId + "]: '" + text + "'");
                continue;
            }

            Map<String, Object> structuredInfo = structuredMap.get(itemId);

            LogKit.info("处理OCR元素 - text: '" + text + "', type: '" + type + "', pos: " + pos +
                       ", structured: " + (structuredInfo != null ? structuredInfo.get("sub_type") : "null"));

            // 对于image类型（条码/二维码），text字段通常为null，这是正常的
            if (pos == null || pos.size() < 8) {
                LogKit.warn("跳过无效OCR元素 - pos无效");
                continue;
            }

            // 对于line类型的元素，text不能为空
            if ("line".equals(type) && StrKit.isBlank(text)) {
                LogKit.warn("跳过无效文本元素 - text为空");
                continue;
            }

            // pos数组格式: [x1, y1, x2, y2, x3, y3, x4, y4] (四个角点)
            // 计算边界框
            int minX = Math.min(Math.min(pos.get(0), pos.get(2)),
                               Math.min(pos.get(4), pos.get(6)));
            int minY = Math.min(Math.min(pos.get(1), pos.get(3)),
                               Math.min(pos.get(5), pos.get(7)));
            int maxX = Math.max(Math.max(pos.get(0), pos.get(2)),
                               Math.max(pos.get(4), pos.get(6)));
            int maxY = Math.max(Math.max(pos.get(1), pos.get(3)),
                               Math.max(pos.get(5), pos.get(7)));

            int width = maxX - minX;
            int height = maxY - minY;

            // 根据类型处理不同的元素
            if ("line".equals(type)) {
                // 尝试获取char_positions进行智能charWidth计算
                List<List<Integer>> charPositions = (List<List<Integer>>) ocrItem.get("char_positions");
                double smartCharWidth = 0.0;

                if (charPositions != null && !charPositions.isEmpty()) {
                    // 使用智能算法计算字符宽度
                    smartCharWidth = CoordinateUtils.calculateSmartCharWidth(text, charPositions);
                    LogKit.info("智能计算charWidth: " + smartCharWidth + " for text: " + text);
                } else {
                    // 降级为简单算法
                    smartCharWidth = text.length() > 0 ? (double) width / text.length() : 0.0;
                    LogKit.info("使用简单算法计算charWidth: " + smartCharWidth + " for text: " + text);
                }

                // 智能判断字体样式 - 使用structured信息
                boolean isBold = isTextBold(structuredInfo != null ? structuredInfo : ocrItem);

                // 基于几何分析判断斜体（优先使用char_positions）
                boolean isItalic = false;
                if (charPositions != null && !charPositions.isEmpty()) {
                    isItalic = CoordinateUtils.calculateItalicFromGeometry(text, charPositions);
                    LogKit.info("几何分析判断斜体: " + isItalic + " for text: " + text);
                } else {
                    // 降级：如果没有char_positions，使用简单规则
                    isItalic = isTextItalicFallback(ocrItem);
                    LogKit.info("降级算法判断斜体: " + isItalic + " for text: " + text);
                }

                // 提取TextIn的新增属性
                double rotationAngle = extractRotationAngle(ocrItem);
                String textDirection = extractTextDirection(ocrItem);
                double confidence = extractConfidence(ocrItem);
                boolean isHandwritten = extractHandwritten(ocrItem);

                // 添加文本元素（使用完整属性）
                response.addTextElement(minX, minY, width, height, text, isBold, isItalic, smartCharWidth,
                                      rotationAngle, textDirection, confidence, isHandwritten);

            } else if ("image".equals(type)) {
                // 处理图像类型（条形码/二维码）
                String subType = (String) ocrItem.get("sub_type");
                LogKit.info("处理image元素，sub_type: " + subType);

                if ("barcode".equals(subType)) {
                    // 条形码处理 - 需要从后续的line元素获取条码内容
                    String barcodeContent = findBarcodeContent(contentList, ocrItem);
                    if (!StrKit.isBlank(barcodeContent)) {
                        LogKit.info("识别到条形码: " + barcodeContent + ", 位置: [" + minX + "," + minY + "," + width + "," + height + "]");
                        response.addBarcodeElement(minX, minY, width, height, barcodeContent, "CODE_128");
                    } else {
                        // 即使内容为空也要添加条形码元素，后续由ZXing补充
                        LogKit.info("条形码图像已识别，内容为空，将由ZXing补充");
                        response.addBarcodeElement(minX, minY, width, height, "", "CODE_128");
                    }
                } else if ("qrcode".equals(subType)) {
                    // 二维码处理 - 需要从TextIn的data字段或后续元素获取二维码内容
                    String qrCodeContent = findQrCodeContent(contentList, ocrItem);
                    if (!StrKit.isBlank(qrCodeContent)) {
                        LogKit.info("识别到二维码: " + qrCodeContent + ", 位置: [" + minX + "," + minY + "," + width + "," + height + "]");
                        response.addQrCodeElement(minX, minY, width, height, qrCodeContent);
                    } else {
                        // 即使内容为空也要添加二维码元素，后续由ZXing补充
                        LogKit.info("二维码图像已识别，内容为空，将由ZXing补充");
                        response.addQrCodeElement(minX, minY, width, height, "");
                    }
                } else if ("chart".equals(subType) || "stamp".equals(subType) ||
                          "logo".equals(subType) || "icon".equals(subType) || "graphic".equals(subType)) {
                    // 图片元素处理 - 图表、印章、logo、图标等
                    String imageUrl = findImageUrlFromContent(ocrItem);
                    String imageType = mapImageSubType(subType);
                    double confidence = extractConfidence(ocrItem);

                    if (!StrKit.isBlank(imageUrl)) {
                        LogKit.info("识别到图片元素: " + imageType + " 位置: [" + minX + "," + minY + "," + width + "," + height + "]");
                        response.addImageElement(minX, minY, width, height, imageUrl, imageType, confidence, true);
                    } else {
                        LogKit.info("识别到图片区域但未找到imageUrl: " + subType + " 位置: [" + minX + "," + minY + "," + width + "," + height + "]");
                    }

                } else {
                    // 对于所有其他未知的sub_type，先尝试智能识别是否为条码
                    LogKit.info("发现未知image sub_type: " + subType + "，尝试智能识别是否为条码");
                    String unknownImageUrl = findImageUrlFromContent(ocrItem);
                    double unknownConfidence = extractConfidence(ocrItem);

                    // 【新增】尝试智能识别是否为二维码或条形码
                    BarcodeDetectionResult detectionResult = tryDetectBarcodeFromImage(unknownImageUrl, minX, minY, width, height);

                    if (detectionResult.isDetected()) {
                        // 识别成功，添加相应的条码元素
                        if ("qrcode".equals(detectionResult.type)) {
                            LogKit.info("✅ 未知sub_type智能识别为二维码[raw]，内容: " + detectionResult.content);
                            response.addQrCodeElement(minX, minY, width, height, detectionResult.content);
                        } else if ("barcode".equals(detectionResult.type)) {
                            LogKit.info("✅ 未知sub_type智能识别为条形码[raw]，内容: " + detectionResult.content);
                            response.addBarcodeElement(minX, minY, width, height, detectionResult.content, "CODE_128");
                        }

                        // 保存图片URL和原始sub_type到元素中（用于调试和验证）
                        if (!StrKit.isBlank(unknownImageUrl) && !response.getElements().isEmpty()) {
                            Map<String, Object> lastElement = response.getElements().get(response.getElements().size() - 1);
                            lastElement.put("imageUrl", unknownImageUrl);
                            lastElement.put("detectionMethod", detectionResult.method);
                            lastElement.put("originalSubType", subType);
                        }
                    } else {
                        // 智能识别失败，确实是普通图片
                        String unknownImageType = mapImageSubType(subType); // 会返回"generic"

                        if (!StrKit.isBlank(unknownImageUrl)) {
                            LogKit.info("未知sub_type智能识别失败，作为通用图片处理[raw]: " + unknownImageType + " 位置: [" + minX + "," + minY + "," + width + "," + height + "]");
                            response.addImageElement(minX, minY, width, height, unknownImageUrl, unknownImageType, unknownConfidence, true);
                        } else {
                            LogKit.info("识别到图片区域但未找到imageUrl: " + subType + " 位置: [" + minX + "," + minY + "," + width + "," + height + "]");
                        }
                    }
                }

            } else if ("table".equals(type)) {
                // 表格处理 - 利用TextIn的完整表格数据
                List<Map<String, Object>> textinCells = (List<Map<String, Object>>) ocrItem.get("cells");

                if (textinCells != null && !textinCells.isEmpty()) {
                    // 解析TextIn的表格结构
                    TableStructure tableStructure = parseTextInTable(textinCells);

                    LogKit.info("解析表格: " + tableStructure.rows + "行 x " + tableStructure.cols + "列, " +
                               tableStructure.cells.size() + "个单元格");

                    // 对于原始OCR结果中的表格，也尝试提取尺寸信息
                    TableDimensions dimensions = extractTableDimensions();
                    if (dimensions != null) {
                        LogKit.info("原始OCR表格尺寸信息: 行高=" + dimensions.rowHeights + ", 列宽=" + dimensions.columnWidths);
                        response.addTableElement(minX, minY, width, height,
                                               tableStructure.rows, tableStructure.cols, tableStructure.cells,
                                               dimensions.rowHeights, dimensions.columnWidths);
                    } else {
                        response.addTableElement(minX, minY, width, height,
                                               tableStructure.rows, tableStructure.cols, tableStructure.cells);
                    }
                } else {
                    // 降级处理：如果没有cells数据，创建单个单元格
                    List<Map<String, Object>> fallbackCells = new ArrayList<>();
                    Map<String, Object> cellData = new HashMap<>();
                    cellData.put("row", 0);
                    cellData.put("col", 0);
                    cellData.put("content", text != null ? text : "");
                    fallbackCells.add(cellData);

                    LogKit.info("表格降级处理: 创建1行1列单元格");
                    response.addTableElement(minX, minY, width, height, 1, 1, fallbackCells);
                }
            }
        }
    }

    /**
     * 转换structured数组中的结构化元素
     * structured数组包含表格等高级结构信息
     */
    @SuppressWarnings("unchecked")
    private void convertStructuredElements(Map<String, Object> pageData, OcrResponse response) {
        List<Map<String, Object>> structuredList = (List<Map<String, Object>>) pageData.get("structured");
        if (structuredList == null || structuredList.isEmpty()) {
            LogKit.info("structured数组为空，跳过结构化元素处理");
            return;
        }

        // 保存content数组的引用，用于获取单元格文本
        List<Map<String, Object>> contentList = (List<Map<String, Object>>) pageData.get("content");
        this.currentContentList = contentList;

        LogKit.info("处理structured数组，数量: " + structuredList.size());

        for (Map<String, Object> structuredItem : structuredList) {
            String type = (String) structuredItem.get("type");
            LogKit.info("处理structured元素: type=" + type);

            if ("table".equals(type)) {
                convertStructuredTable(structuredItem, response);
            }
            // 未来可以在此扩展处理其他结构化类型：图表、段落等
        }
    }

    /**
     * 转换structured数组中的表格元素
     */
    @SuppressWarnings("unchecked")
    private void convertStructuredTable(Map<String, Object> tableItem, OcrResponse response) {
        try {
            LogKit.info("转换structured表格元素");

            // 获取表格基本信息
            List<Integer> pos = (List<Integer>) tableItem.get("pos");
            Integer rows = (Integer) tableItem.get("rows");
            Integer cols = (Integer) tableItem.get("cols");

            if (pos == null || pos.size() < 8 || rows == null || cols == null) {
                LogKit.warn("表格基本信息不完整，跳过");
                return;
            }

            // 计算表格边界
            int minX = Math.min(Math.min(pos.get(0), pos.get(2)),
                               Math.min(pos.get(4), pos.get(6)));
            int minY = Math.min(Math.min(pos.get(1), pos.get(3)),
                               Math.min(pos.get(5), pos.get(7)));
            int maxX = Math.max(Math.max(pos.get(0), pos.get(2)),
                               Math.max(pos.get(4), pos.get(6)));
            int maxY = Math.max(Math.max(pos.get(1), pos.get(3)),
                               Math.max(pos.get(5), pos.get(7)));

            int width = maxX - minX;
            int height = maxY - minY;

            LogKit.info("表格尺寸: " + rows + "行 x " + cols + "列, 位置: [" + minX + "," + minY + "," + width + "," + height + "]");

            // 检查是否为边框（1行1列）
            if (rows == 1 && cols == 1) {
                // 获取表格单元格数据
                List<Map<String, Object>> cells = (List<Map<String, Object>>) tableItem.get("cells");

                // 检查单元格内容是否为空
                boolean isBorder = true;
                if (cells != null && !cells.isEmpty()) {
                    for (Map<String, Object> cell : cells) {
                        String cellText = getCellTextFromContent(cell);
                        if (!StrKit.isBlank(cellText)) {
                            isBorder = false;
                            break;
                        }
                    }
                }

                if (isBorder) {
                    LogKit.info("识别为边框元素[structured]: 位置=[" + minX + "," + minY + "," + width + "," + height + "]");
                    response.addBorderElement(minX, minY, width, height);
                    return;
                }
            }

            // 获取表格单元格数据
            List<Map<String, Object>> cells = (List<Map<String, Object>>) tableItem.get("cells");
            if (cells != null && !cells.isEmpty()) {
                List<Map<String, Object>> convertedCells = new ArrayList<>();

                for (Map<String, Object> cell : cells) {
                    Map<String, Object> convertedCell = convertStructuredTableCell(cell);
                    if (convertedCell != null) {
                        convertedCells.add(convertedCell);
                    }
                }

                // 提取表格尺寸信息
                TableDimensions dimensions = extractTableDimensionsFromStructured(tableItem);

                LogKit.info("转换表格单元格: " + convertedCells.size() + "个");

                // 【新增】检测structured表格单元格内的条码并生成独立组件
                detectBarcodesInStructuredTableCells(cells, minX, minY, response);

                if (dimensions != null) {
                    LogKit.info("structured表格尺寸信息: 行高=" + dimensions.rowHeights + ", 列宽=" + dimensions.columnWidths);
                    response.addTableElement(minX, minY, width, height, rows, cols, convertedCells,
                                            dimensions.rowHeights, dimensions.columnWidths);
                } else {
                    LogKit.warn("structured表格未找到尺寸信息，使用默认方法");
                    response.addTableElement(minX, minY, width, height, rows, cols, convertedCells);
                }

            } else {
                // 没有cells数据，如果是1行1列则为边框
                if (rows == 1 && cols == 1) {
                    LogKit.info("表格没有cells数据且为1行1列，识别为边框元素");
                    response.addBorderElement(minX, minY, width, height);
                } else {
                    LogKit.warn("表格没有cells数据，使用默认处理");
                    // 降级处理
                    List<Map<String, Object>> defaultCells = new ArrayList<>();
                    Map<String, Object> defaultCell = new HashMap<>();
                    defaultCell.put("row", 0);
                    defaultCell.put("col", 0);
                    defaultCell.put("content", "");
                    defaultCells.add(defaultCell);

                    response.addTableElement(minX, minY, width, height, rows, cols, defaultCells);
                }
            }

        } catch (Exception e) {
            LogKit.error("转换structured表格时出错: " + e.getMessage(), e);
        }
    }

    /**
     * 转换structured表格的单元格
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> convertStructuredTableCell(Map<String, Object> structuredCell) {
        try {
            Map<String, Object> convertedCell = new HashMap<>();

            // 获取基本位置信息
            Integer row = (Integer) structuredCell.get("row");
            Integer col = (Integer) structuredCell.get("col");

            convertedCell.put("row", row != null ? row : 0);
            convertedCell.put("col", col != null ? col : 0);

            // 获取跨行跨列信息
            Integer rowSpan = (Integer) structuredCell.get("row_span");
            Integer colSpan = (Integer) structuredCell.get("col_span");

            if (rowSpan != null && rowSpan > 1) {
                convertedCell.put("rowSpan", rowSpan);
            }
            if (colSpan != null && colSpan > 1) {
                convertedCell.put("colSpan", colSpan);
            }

            // 获取单元格内容
            // structured表格的单元格内容通过content字段关联到content数组
            String rawCellText = getCellTextFromContent(structuredCell);
            // 先清理markdown格式符号
            String cellText = cleanMarkdownFormatting(rawCellText);
            // 处理单元格内容中的换行符和img标签
            String processedCellText = processCellContentLineBreaks(cellText);
            convertedCell.put("content", processedCellText != null ? processedCellText : "");

            LogKit.debug("转换structured单元格: (" + row + "," + col + ") = '" + convertedCell.get("content") + "'");

            return convertedCell;

        } catch (Exception e) {
            LogKit.warn("转换structured单元格时出错: " + e.getMessage());
            return null;
        }
    }

    /**
     * 从content数组中获取单元格的文本内容
     * structured表格通过content字段关联到content数组中的对应元素
     */
    @SuppressWarnings("unchecked")
    private String getCellTextFromContent(Map<String, Object> structuredCell) {
        try {
            // structured单元格通过content字段关联到具体的文本内容
            List<Map<String, Object>> contentRefs = (List<Map<String, Object>>) structuredCell.get("content");
            if (contentRefs == null || contentRefs.isEmpty()) {
                return "";
            }

            // 每个content引用包含textblock类型和对应的content数组索引
            StringBuilder cellText = new StringBuilder();
            for (Map<String, Object> contentRef : contentRefs) {
                String type = (String) contentRef.get("type");
                if ("textblock".equals(type)) {
                    List<Integer> contentIndices = (List<Integer>) contentRef.get("content");
                    if (contentIndices != null) {
                        for (Integer index : contentIndices) {
                            String text = getTextFromContentByIndex(index);
                            if (text != null && !text.trim().isEmpty()) {
                                if (cellText.length() > 0) {
                                    cellText.append(" ");
                                }
                                cellText.append(text);
                            }
                        }
                    }
                }
            }

            return cellText.toString();

        } catch (Exception e) {
            LogKit.warn("获取单元格文本内容时出错: " + e.getMessage());
            return "";
        }
    }

    // 保存当前处理的页面数据，以便各个方法协同使用
    private Map<String, Object> currentPageData = null;
    private List<Map<String, Object>> currentContentList = null;
    private List<Map<String, Object>> currentStructuredList = null;
    // 原图文件句柄（用于在 sub_type 为空时进行本地裁剪兜底）
    private File currentImageFile = null;


    /**
     * 根据索引从当前的content数组获取文本
     */
    private String getTextFromContentByIndex(Integer index) {
        if (currentContentList == null || index == null || index < 0 || index >= currentContentList.size()) {
            return null;
        }

        Map<String, Object> contentItem = currentContentList.get(index);
        String rawText = (String) contentItem.get("text");
        // 作为后备保险，清理可能存在的markdown格式符号
        return cleanMarkdownFormatting(rawText);
    }

    /**
     * 转换文本元素（保留原方法以兼容测试）
     */
    private void convertTextElements(List<Map<String, Object>> texts, OcrResponse response) {
        if (texts == null || texts.isEmpty()) {
            return;
        }

        LogKit.info("转换文本元素，数量: " + texts.size());

        // 这个方法现在主要用于测试兼容性，实际使用convertRawOcrElements
        LogKit.info("使用兼容性文本转换方法");
        for (Map<String, Object> textMap : texts) {
            String text = (String) textMap.get("text");
            if (StrKit.isBlank(text)) {
                continue;
            }

            // 简化处理，使用默认值
            response.addTextElement(10, 10, 50, 20, text, false, false);
        }
    }

    /**
     * 转换表格元素（兼容性方法）
     */
    private void convertTableElements(List<Map<String, Object>> tables, OcrResponse response) {
        if (tables == null || tables.isEmpty()) {
            return;
        }

        LogKit.info("转换表格元素，数量: " + tables.size());

        for (Map<String, Object> tableMap : tables) {
            // 简化处理，使用默认值
            List<Map<String, Object>> cells = new ArrayList<>();
            Map<String, Object> cellData = new HashMap<>();
            cellData.put("row", 0);
            cellData.put("col", 0);
            cellData.put("content", "表格内容");
            cells.add(cellData);

            // 添加表格元素
            response.addTableElement(10, 50, 100, 50, 1, 1, cells);
        }
    }

    /**
     * 转换条码元素（兼容性方法）
     */
    private void convertBarcodeElements(List<Map<String, Object>> barcodes, OcrResponse response) {
        if (barcodes == null || barcodes.isEmpty()) {
            return;
        }

        LogKit.info("转换条码元素，数量: " + barcodes.size());

        for (Map<String, Object> barcodeMap : barcodes) {
            String text = (String) barcodeMap.get("text");
            String type = (String) barcodeMap.get("type");

            if (StrKit.isBlank(text)) {
                continue;
            }

            // 简化处理，使用默认值
            // 判断是条形码还是二维码
            if (isQRCode(type)) {
                // 添加二维码元素
                response.addQrCodeElement(20, 30, 80, 80, text);
            } else {
                // 添加条形码元素
                String barcodeType = mapBarcodeType(type);
                response.addBarcodeElement(20, 30, 80, 30, text, barcodeType);
            }
        }
    }

    /**
     * 收集需要ZXing补充内容的条码
     * 查找所有已添加到response中但内容为空的条码元素
     */
    @SuppressWarnings("unchecked")
    private void collectPendingBarcodes(OcrResponse response, List<PendingBarcode> pendingBarcodes) {
        if (response.getElements() == null) {
            return;
        }

        List<Map<String, Object>> elements = response.getElements();
        for (int i = 0; i < elements.size(); i++) {
            Map<String, Object> element = elements.get(i);
            String elementType = String.valueOf(element.get("elementType"));

            // elementType: "2" = 条形码, "7" = 二维码
            if ("2".equals(elementType) || "7".equals(elementType)) {
                String content = (String) element.get("content");

                // 如果内容为空或只是占位符，则需要ZXing补充
                if (StrKit.isBlank(content) || "UNKNOWN".equals(content)) {
                    Integer x = (Integer) element.get("x");
                    Integer y = (Integer) element.get("y");
                    Integer width = (Integer) element.get("width");
                    Integer height = (Integer) element.get("height");
                    String imageUrl = (String) element.get("imageUrl");  // 获取TextIn返回的图片URL

                    if (x != null && y != null && width != null && height != null) {
                        String type = "7".equals(elementType) ? "qrcode" : "barcode";
                        PendingBarcode pending = new PendingBarcode(type, x, y, width, height, i, imageUrl);
                        pendingBarcodes.add(pending);
                        LogKit.info("收集待补充条码: type=" + type + ", pos=[" + x + "," + y + "," + width + "," + height + "]" +
                                   (imageUrl != null ? ", imageUrl=" + imageUrl : ""));
                    }
                }
            }
        }
    }

    /**
     * 使用ZXing检测条码（仅补充TextIn识别到但没有内容的条码）
     *
     * @param imageFile 图片文件
     * @param response OCR响应对象
     * @param pendingBarcodes TextIn识别到但需要补充内容的条码列表
     */
    @SuppressWarnings("unchecked")
    private void detectBarcodesWithZXing(File imageFile, OcrResponse response, List<PendingBarcode> pendingBarcodes) {
        try {
            LogKit.info("使用ZXing对 " + pendingBarcodes.size() + " 个条码进行内容补充");

            // 预先读取原图（仅在有需要时使用）
            BufferedImage fullImage = null;
            boolean imageLoaded = false;

            // 对每个待补充的条码进行处理
            for (PendingBarcode pending : pendingBarcodes) {
                try {
                    String decodedContent = null;

                    // 优先策略：如果有TextIn提供的imageUrl，优先使用URL识别
                    if (!StrKit.isBlank(pending.imageUrl)) {
                        LogKit.info("优先使用TextIn提供的imageUrl进行识别: " + pending.imageUrl);
                        decodedContent = decodeFromImageUrl(pending.imageUrl, pending.type);

                        if (!StrKit.isBlank(decodedContent)) {
                            LogKit.info("URL识别成功: " + decodedContent);
                        } else {
                            LogKit.info("URL识别失败，降级到本地图片裁剪识别");
                        }
                    }

                    // 降级策略：如果URL识别失败或没有URL，使用本地图片裁剪识别
                    if (StrKit.isBlank(decodedContent)) {
                        // 延迟加载原图（仅在需要时加载）
                        if (!imageLoaded) {
                            fullImage = ImageIO.read(imageFile);
                            if (fullImage == null) {
                                LogKit.warn("无法读取图片文件");
                                return;
                            }
                            imageLoaded = true;
                        }

                        // 裁剪图片到条码区域（稍微扩大一点边界以提高识别率）
                        int padding = 10;
                        int cropX = Math.max(0, pending.x - padding);
                        int cropY = Math.max(0, pending.y - padding);
                        int cropWidth = Math.min(fullImage.getWidth() - cropX, pending.width + 2 * padding);
                        int cropHeight = Math.min(fullImage.getHeight() - cropY, pending.height + 2 * padding);

                        BufferedImage croppedImage = fullImage.getSubimage(cropX, cropY, cropWidth, cropHeight);

                        LogKit.info("尝试本地裁剪识别: type=" + pending.type +
                                   ", 原始位置=[" + pending.x + "," + pending.y + "," + pending.width + "," + pending.height + "]" +
                                   ", 裁剪区域=[" + cropX + "," + cropY + "," + cropWidth + "," + cropHeight + "]");

                        // 根据类型使用不同的识别策略
                        if ("qrcode".equals(pending.type)) {
                            // 先尝试优化参数的识别
                            decodedContent = decodeQRCodeWithOptimizedParams(croppedImage);
                            if (StrKit.isBlank(decodedContent)) {
                                // 降级到原有方法
                                decodedContent = decodeQRCode(croppedImage);
                            }
                        } else {
                            // 先尝试优化参数的识别
                            decodedContent = decodeBarcodeWithOptimizedParams(croppedImage);
                            if (StrKit.isBlank(decodedContent)) {
                                // 降级到原有方法
                                decodedContent = decodeBarcode(croppedImage);
                            }
                        }
                    }

                    // 如果成功解码，更新对应元素的内容
                    if (!StrKit.isBlank(decodedContent)) {
                        List<Map<String, Object>> elements = response.getElements();
                        if (pending.elementIndex < elements.size()) {
                            Map<String, Object> element = elements.get(pending.elementIndex);
                            element.put("content", decodedContent);
                            LogKit.info("成功补充条码内容: " + decodedContent);
                        }
                    } else {
                        LogKit.info("无法识别该条码内容（尝试了所有方法）");
                    }

                } catch (Exception e) {
                    LogKit.warn("处理条码时出错: " + e.getMessage());
                }
            }

        } catch (Exception e) {
            LogKit.warn("ZXing条码检测失败: " + e.getMessage());
            // 不抛出异常，因为这只是补充检测
        }
    }

    /**
     * 从图片URL下载并识别条码
     *
     * @param imageUrl 图片URL
     * @param barcodeType 条码类型（"qrcode" 或 "barcode"）
     * @return 识别到的内容，如果无法识别则返回null
     */
    private String decodeFromImageUrl(String imageUrl, String barcodeType) {
        if (StrKit.isBlank(imageUrl)) {
            return null;
        }

        try {
            LogKit.info("从URL下载并识别条码: " + imageUrl);

            // 从URL下载图片
            URL url = new URL(imageUrl);
            BufferedImage image = ImageIO.read(url);

            if (image == null) {
                LogKit.warn("无法从URL加载图片: " + imageUrl);
                return null;
            }

            LogKit.info("成功下载图片，尺寸: " + image.getWidth() + "x" + image.getHeight());

            // 根据类型选择识别方法
            String result = null;
            if ("qrcode".equals(barcodeType)) {
                result = decodeQRCodeWithOptimizedParams(image);
            } else {
                result = decodeBarcodeWithOptimizedParams(image);
            }

            if (!StrKit.isBlank(result)) {
                LogKit.info("成功从URL识别条码内容: " + result);
            } else {
                LogKit.warn("无法从URL识别条码内容，可能原因：图片质量、格式或ZXing参数不匹配");
            }

            return result;

        } catch (Exception e) {
            LogKit.warn("从URL识别条码失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 使用原图进行本地裁剪兜底识别（适用于 TextIn 子图被裁掉导致无法识别的情况）
     */
    private String detectBarcodeByLocalCrop(int x, int y, int w, int h) {
        if (currentImageFile == null || !currentImageFile.exists()) {
            return null;
        }
        try {
            BufferedImage full = ImageIO.read(currentImageFile);
            if (full == null) return null;

            // 多组 padding 策略：像素与比例混合，最多尝试若干次
            int[] paddingsPx = new int[] { 16, 32, 48 };
            double[] paddingsRatio = new double[] { 0.05, 0.10, 0.15 };

            for (int pad : paddingsPx) {
                String r = tryCropAndDecode(full, x, y, w, h, pad);
                if (!StrKit.isBlank(r)) return r;
            }
            for (double ratio : paddingsRatio) {
                int pad = (int) Math.round(Math.max(w, h) * ratio);
                String r = tryCropAndDecode(full, x, y, w, h, pad);
                if (!StrKit.isBlank(r)) return r;
            }
        } catch (Exception e) {
            LogKit.debug("detectBarcodeByLocalCrop error: " + e.getMessage());
        }
        return null;
    }

    private String tryCropAndDecode(BufferedImage full, int x, int y, int w, int h, int padding) {
        int cropX = Math.max(0, x - padding);
        int cropY = Math.max(0, y - padding);
        int cropW = Math.min(full.getWidth() - cropX, w + 2 * padding);
        int cropH = Math.min(full.getHeight() - cropY, h + 2 * padding);
        if (cropW <= 5 || cropH <= 5) return null;
        try {
            BufferedImage region = full.getSubimage(cropX, cropY, cropW, cropH);
            // 优先按二维码策略，再尝试条码策略
            String r = decodeQRCodeWithOptimizedParams(region);
            if (!StrKit.isBlank(r)) return r;
            r = decodeQRCode(region);
            if (!StrKit.isBlank(r)) return r;
            r = decodeBarcodeWithOptimizedParams(region);
            if (!StrKit.isBlank(r)) return r;
            r = decodeBarcode(region);
            if (!StrKit.isBlank(r)) return r;
        } catch (Exception e) {
            LogKit.debug("tryCropAndDecode error: " + e.getMessage());
        }
        return null;
    }





    /**
     * 【新增方法】记录智能识别的统计信息
     * 用于监控和优化智能识别效果
     *
     * @param response 转换后的响应数据
     */
    private void logSmartDetectionStatistics(OcrResponse response) {
        try {
            int totalElements = response.getElements() != null ? response.getElements().size() : 0;
            int smartDetectedQR = 0;
            int smartDetectedBarcode = 0;
            int smartDetectedTotal = 0;
            int originalImages = 0;

            Map<String, Integer> methodCount = new HashMap<>();
            Map<String, Integer> originalSubTypeCount = new HashMap<>();

            if (response.getElements() != null) {
                for (Map<String, Object> element : response.getElements()) {
                    String elementType = (String) element.get("elementType");
                    String detectionMethod = (String) element.get("detectionMethod");
                    String originalSubType = (String) element.get("originalSubType");

                    // 统计智能识别成功的元素
                    if (detectionMethod != null && !"none".equals(detectionMethod)) {
                        smartDetectedTotal++;

                        if ("7".equals(elementType)) { // 二维码
                            smartDetectedQR++;
                        } else if ("6".equals(elementType)) { // 条形码
                            smartDetectedBarcode++;
                        }

                        // 统计识别方法
                        methodCount.put(detectionMethod, methodCount.getOrDefault(detectionMethod, 0) + 1);

                        // 统计原始sub_type
                        if (originalSubType != null) {
                            originalSubTypeCount.put(originalSubType, originalSubTypeCount.getOrDefault(originalSubType, 0) + 1);
                        }
                    }

                    // 统计普通图片元素
                    if ("8".equals(elementType)) { // 图片
                        originalImages++;
                    }
                }
            }

            // 输出统计日志
            LogKit.info("📊 智能识别统计报告:");
            LogKit.info("   总元素数: " + totalElements);
            LogKit.info("   智能识别成功: " + smartDetectedTotal + " 个");
            LogKit.info("   - 二维码: " + smartDetectedQR + " 个");
            LogKit.info("   - 条形码: " + smartDetectedBarcode + " 个");
            LogKit.info("   普通图片: " + originalImages + " 个");

            if (!methodCount.isEmpty()) {
                LogKit.info("   识别方法分布: " + methodCount);
            }

            if (!originalSubTypeCount.isEmpty()) {
                LogKit.info("   原始sub_type分布: " + originalSubTypeCount);
            }

            // 输出识别效果评估
            double smartDetectionRate = totalElements > 0 ? (double) smartDetectedTotal / totalElements * 100 : 0;
            LogKit.info("   🎯 智能识别率: " + String.format("%.1f%%", smartDetectionRate));

            if (smartDetectedTotal > 0) {
                LogKit.info("   ✅ 智能识别功能正常工作，成功补救了 " + smartDetectedTotal + " 个被误识别的条码");
            } else if (originalImages > 0) {
                LogKit.info("   ℹ️ 本次识别中未发现需要智能识别补救的条码，" + originalImages + " 个图片元素确实为普通图片");
            }

        } catch (Exception e) {
            LogKit.warn("记录智能识别统计信息时出错: " + e.getMessage());
        }
    }

    /**
     * 智能识别图像是否为二维码或条形码的结果数据类
     */
    private static class BarcodeDetectionResult {
        public String type;      // "qrcode" 或 "barcode"，如果未识别到则为null
        public String content;   // 识别到的内容，如果未识别到则为null
        public String method;    // 识别方法："url" 或 "fallback"

        public BarcodeDetectionResult(String type, String content, String method) {
            this.type = type;
            this.content = content;
            this.method = method;
        }

        public boolean isDetected() {
            return type != null && content != null;
        }
    }

    /**
     * 【新增方法】尝试通过ZXing智能识别图像是否为二维码或条形码
     * 用于处理TextIn没有正确识别sub_type的情况
     *
     * @param imageUrl 图片URL（TextIn返回的image_url）
     * @param minX 图像X坐标
     * @param minY 图像Y坐标
     * @param width 图像宽度
     * @param height 图像高度
     * @return 识别结果，如果未识别到条码则type和content为null
     */
    private BarcodeDetectionResult tryDetectBarcodeFromImage(String imageUrl, int minX, int minY, int width, int height) {
        LogKit.info("🔍 尝试智能识别图像是否为条码: imageUrl=" + (imageUrl != null ? "有" : "无") +
                   ", 位置=[" + minX + "," + minY + "," + width + "," + height + "]");

        if (!StrKit.isBlank(imageUrl)) {
            // 优先策略：使用图片URL进行识别
            LogKit.info("使用图片URL进行智能识别: " + imageUrl);

            // 先尝试识别为二维码
            String qrContent = decodeFromImageUrl(imageUrl, "qrcode");

            if (!StrKit.isBlank(qrContent)) {
                LogKit.info("✅ 智能识别成功：二维码内容=" + qrContent);
                return new BarcodeDetectionResult("qrcode", qrContent, "url");
            }

            // 再尝试识别为条形码
            String barcodeContent = decodeFromImageUrl(imageUrl, "barcode");
            if (!StrKit.isBlank(barcodeContent)) {
                LogKit.info("✅ 智能识别成功：条形码内容=" + barcodeContent);
                return new BarcodeDetectionResult("barcode", barcodeContent, "url");
            }

            LogKit.info("⚠️ 图片URL识别失败，图像可能确实不是条码");
        } else {
            LogKit.info("⚠️ 没有图片URL，无法进行智能识别");
        }

        // 未识别到条码
        LogKit.info("❌ 智能识别失败，确认为普通图片");
        return new BarcodeDetectionResult(null, null, "none");
    }

    /**
     * 使用优化参数识别二维码
     *
     * @param image 图片
     * @return 识别到的内容
     */
    private String decodeQRCodeWithOptimizedParams(BufferedImage image) {
        try {
            com.google.zxing.qrcode.QRCodeReader qrReader = new com.google.zxing.qrcode.QRCodeReader();

            // 优化的识别参数
            Map<DecodeHintType, Object> hints = new HashMap<>();
            hints.put(DecodeHintType.CHARACTER_SET, "UTF-8");
            hints.put(DecodeHintType.TRY_HARDER, Boolean.TRUE);
            hints.put(DecodeHintType.PURE_BARCODE, Boolean.TRUE); // 对纯条码图片效果好

            // 尝试多种二值化策略
            BinaryBitmap[] bitmaps = new BinaryBitmap[] {
                new BinaryBitmap(new HybridBinarizer(new BufferedImageLuminanceSource(image))),
                new BinaryBitmap(new GlobalHistogramBinarizer(new BufferedImageLuminanceSource(image)))
            };

            for (BinaryBitmap bitmap : bitmaps) {
                try {
                    Result result = qrReader.decode(bitmap, hints);
                    if (result != null && !StrKit.isBlank(result.getText())) {
                        return result.getText();
                    }
                } catch (Exception e) {
                    // 继续尝试下一个策略
                }
            }

            // 如果PURE_BARCODE失败，尝试不用这个参数
            hints.remove(DecodeHintType.PURE_BARCODE);
            for (BinaryBitmap bitmap : bitmaps) {
                try {
                    Result result = qrReader.decode(bitmap, hints);
                    if (result != null && !StrKit.isBlank(result.getText())) {
                        return result.getText();
                    }
                } catch (Exception e) {
                    // 继续尝试
                }
            }

        } catch (Exception e) {
            LogKit.debug("优化的QR码识别失败: " + e.getMessage());
        }

        return null;
    }

    /**
     * 使用优化参数识别条形码
     * 增强版：针对TextIn提供的条码图片进行多策略识别
     *
     * @param image 图片
     * @return 识别到的内容
     */
    private String decodeBarcodeWithOptimizedParams(BufferedImage image) {
        try {
            MultiFormatReader reader = new MultiFormatReader();

            // 只尝试条形码格式
            List<BarcodeFormat> formats = new ArrayList<>();
            formats.add(BarcodeFormat.CODE_128);
            formats.add(BarcodeFormat.CODE_39);
            formats.add(BarcodeFormat.CODE_93);
            formats.add(BarcodeFormat.EAN_13);
            formats.add(BarcodeFormat.EAN_8);
            formats.add(BarcodeFormat.UPC_A);
            formats.add(BarcodeFormat.UPC_E);
            formats.add(BarcodeFormat.CODABAR);
            formats.add(BarcodeFormat.ITF);

            // 策略1：标准识别参数
            Map<DecodeHintType, Object> hints1 = new HashMap<>();
            hints1.put(DecodeHintType.CHARACTER_SET, "UTF-8");
            hints1.put(DecodeHintType.TRY_HARDER, Boolean.TRUE);
            hints1.put(DecodeHintType.PURE_BARCODE, Boolean.TRUE);
            hints1.put(DecodeHintType.POSSIBLE_FORMATS, formats);

            // 策略2：不使用PURE_BARCODE参数（适合有背景的图片）
            Map<DecodeHintType, Object> hints2 = new HashMap<>();
            hints2.put(DecodeHintType.CHARACTER_SET, "UTF-8");
            hints2.put(DecodeHintType.TRY_HARDER, Boolean.TRUE);
            hints2.put(DecodeHintType.PURE_BARCODE, Boolean.FALSE);
            hints2.put(DecodeHintType.POSSIBLE_FORMATS, formats);

            // 策略3：最宽松的参数
            Map<DecodeHintType, Object> hints3 = new HashMap<>();
            hints3.put(DecodeHintType.CHARACTER_SET, "UTF-8");
            hints3.put(DecodeHintType.TRY_HARDER, Boolean.TRUE);
            hints3.put(DecodeHintType.POSSIBLE_FORMATS, formats);

            @SuppressWarnings("unchecked")
            Map<DecodeHintType, Object>[] allHints = new Map[]{hints1, hints2, hints3};

            // 尝试多种二值化策略
            BinaryBitmap[] bitmaps = new BinaryBitmap[] {
                new BinaryBitmap(new HybridBinarizer(new BufferedImageLuminanceSource(image))),
                new BinaryBitmap(new GlobalHistogramBinarizer(new BufferedImageLuminanceSource(image)))
            };

            // 组合尝试所有策略
            for (Map<DecodeHintType, Object> hints : allHints) {
                for (BinaryBitmap bitmap : bitmaps) {
                    try {
                        Result result = reader.decode(bitmap, hints);
                        if (result != null && !StrKit.isBlank(result.getText())) {
                            LogKit.debug("条码识别成功，使用策略: " + (hints == hints1 ? "标准" : hints == hints2 ? "无背景" : "宽松"));
                            return result.getText();
                        }
                    } catch (Exception e) {
                        // 继续尝试下一个策略
                    }
                }
            }

        } catch (Exception e) {
            LogKit.debug("优化的条形码识别失败: " + e.getMessage());
        }

        return null;
    }

    /**
     * 解码二维码（仅在指定区域）
     *
     * @param image 裁剪后的图片
     * @return 解码后的内容，如果无法解码则返回null
     */
    private String decodeQRCode(BufferedImage image) {
        try {
            // 使用QR码专用Reader
            com.google.zxing.qrcode.QRCodeReader qrReader = new com.google.zxing.qrcode.QRCodeReader();

            // 尝试多种二值化策略
            BinaryBitmap[] bitmaps = new BinaryBitmap[] {
                new BinaryBitmap(new HybridBinarizer(new BufferedImageLuminanceSource(image))),
                new BinaryBitmap(new GlobalHistogramBinarizer(new BufferedImageLuminanceSource(image)))
            };

            Map<DecodeHintType, Object> hints = new HashMap<>();
            hints.put(DecodeHintType.CHARACTER_SET, "UTF-8");
            hints.put(DecodeHintType.TRY_HARDER, Boolean.TRUE);
            hints.put(DecodeHintType.PURE_BARCODE, Boolean.FALSE);

            for (BinaryBitmap bitmap : bitmaps) {
                try {
                    Result result = qrReader.decode(bitmap, hints);
                    if (result != null && !StrKit.isBlank(result.getText())) {
                        LogKit.info("QR码解码成功: " + result.getText());
                        return result.getText();
                    }
                } catch (Exception e) {
                    // 继续尝试下一个策略
                }
            }

        } catch (Exception e) {
            LogKit.debug("QR码解码失败: " + e.getMessage());
        }

        return null;
    }

    /**
     * 解码条形码（仅在指定区域）
     *
     * @param image 裁剪后的图片
     * @return 解码后的内容，如果无法解码则返回null
     */
    private String decodeBarcode(BufferedImage image) {
        try {
            MultiFormatReader reader = new MultiFormatReader();

            // 设置条形码相关的提示
            Map<DecodeHintType, Object> hints = new HashMap<>();
            hints.put(DecodeHintType.CHARACTER_SET, "UTF-8");
            hints.put(DecodeHintType.TRY_HARDER, Boolean.TRUE);

            // 只尝试条形码格式，不包括QR码
            List<BarcodeFormat> formats = new ArrayList<>();
            formats.add(BarcodeFormat.CODE_128);
            formats.add(BarcodeFormat.CODE_39);
            formats.add(BarcodeFormat.CODE_93);
            formats.add(BarcodeFormat.EAN_13);
            formats.add(BarcodeFormat.EAN_8);
            formats.add(BarcodeFormat.UPC_A);
            formats.add(BarcodeFormat.UPC_E);
            formats.add(BarcodeFormat.CODABAR);
            formats.add(BarcodeFormat.ITF);
            hints.put(DecodeHintType.POSSIBLE_FORMATS, formats);

            BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(
                new BufferedImageLuminanceSource(image)));

            Result result = reader.decode(bitmap, hints);
            if (result != null && !StrKit.isBlank(result.getText())) {
                LogKit.info("条形码解码成功: " + result.getBarcodeFormat() + " - " + result.getText());
                return result.getText();
            }

        } catch (Exception e) {
            LogKit.debug("条形码解码失败: " + e.getMessage());
        }

        return null;
    }

    /**
     * 使用MultiFormatReader进行通用条码检测（已废弃，保留用于兼容）
     */
    @Deprecated
    private void detectWithMultiFormatReader(BufferedImage image, OcrResponse response) {
        try {
            BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(
                new BufferedImageLuminanceSource(image)));

            MultiFormatReader reader = new MultiFormatReader();
            Result result = reader.decode(bitmap);

            if (result != null && !StrKit.isBlank(result.getText())) {
                LogKit.info("ZXing MultiFormat检测到条码: " + result.getBarcodeFormat() + " - " + result.getText());

                // 废弃：不再进行全图扫描，以下代码保留用于兼容
                // if (!isValidBarcodeContent(result.getText(), result.getBarcodeFormat().toString())) {
                //     LogKit.warn("ZXing识别结果不合理，跳过: " + result.getText());
                //     return;
                // }

                // 提取ZXing的位置信息
                int[] bounds = calculateZXingBounds(result);
                int x = bounds[0];
                int y = bounds[1];
                int width = bounds[2];
                int height = bounds[3];

                LogKit.info("ZXing条码位置: [" + x + "," + y + "," + width + "," + height + "]");

                // 废弃：不再进行全图扫描，以下代码保留用于兼容
                // if (isPositionOverlapWithExistingElements(response, x, y, width, height)) {
                //     LogKit.warn("ZXing识别位置与已有元素重叠，跳过");
                //     return;
                // }

                if (isQRCodeFormat(result.getBarcodeFormat().toString())) {
                    // 检查是否已经有相同内容的二维码
                    if (!isDuplicateQRCode(response, result.getText())) {
                        response.addQrCodeElement(x, y, width, height, result.getText());
                        LogKit.info("ZXing添加二维码: " + result.getText());
                    } else {
                        LogKit.info("ZXing跳过重复的二维码: " + result.getText());
                    }
                } else {
                    // 检查是否已经有相同内容的条形码
                    if (!isDuplicateBarcode(response, result.getText())) {
                        String barcodeType = mapZXingBarcodeType(result.getBarcodeFormat().toString());
                        response.addBarcodeElement(x, y, width, height, result.getText(), barcodeType);
                        LogKit.info("ZXing添加条形码: " + result.getText());
                    } else {
                        LogKit.info("ZXing跳过重复的条形码: " + result.getText());
                    }
                }
            }

        } catch (Exception e) {
            LogKit.debug("MultiFormatReader检测异常: " + e.getMessage());
        }
    }

    /**
     * 专门针对QR码的优化检测（已废弃，保留用于兼容）
     * 使用专用的QRCodeReader和优化参数
     * @deprecated 现在只对TextIn识别到的条码位置进行补充，不再进行全图扫描
     */
    @Deprecated
    private void detectQRCodeSpecifically(BufferedImage image, OcrResponse response) {
        try {
            // 导入QR码专用Reader
            com.google.zxing.qrcode.QRCodeReader qrReader = new com.google.zxing.qrcode.QRCodeReader();

            // 尝试多种二值化策略
            BinaryBitmap[] bitmaps = {
                // 策略1：HybridBinarizer（默认）
                new BinaryBitmap(new HybridBinarizer(new BufferedImageLuminanceSource(image))),
                // 策略2：GlobalHistogramBinarizer（对低质量图像更友好）
                new BinaryBitmap(new com.google.zxing.common.GlobalHistogramBinarizer(new BufferedImageLuminanceSource(image)))
            };

            for (int i = 0; i < bitmaps.length; i++) {
                try {
                    Result qrResult = qrReader.decode(bitmaps[i]);

                    if (qrResult != null && !StrKit.isBlank(qrResult.getText())) {
                        LogKit.info("ZXing QR专用检测成功 (策略" + (i+1) + "): " + qrResult.getText());

                        // 废弃：不再进行全图扫描，以下代码保留用于兼容
                        // if (!isValidBarcodeContent(qrResult.getText(), "QR_CODE")) {
                        //     LogKit.warn("QR码内容不合理，跳过: " + qrResult.getText());
                        //     continue;
                        // }

                        // 获取位置信息
                        int[] bounds = calculateZXingBounds(qrResult);

                        // 废弃：不再进行全图扫描，以下代码保留用于兼容
                        // if (isPositionOverlapWithExistingElements(response, bounds[0], bounds[1], bounds[2], bounds[3])) {
                        //     LogKit.warn("QR码位置与已有元素重叠，跳过");
                        //     continue;
                        // }

                        // 检查是否已经有相同内容的二维码（避免重复添加）
                        if (!isDuplicateQRCode(response, qrResult.getText())) {
                            response.addQrCodeElement(bounds[0], bounds[1], bounds[2], bounds[3], qrResult.getText());
                            LogKit.info("添加QR码到响应: " + qrResult.getText());
                        } else {
                            LogKit.info("跳过重复的QR码: " + qrResult.getText());
                        }

                        return; // 找到一个就够了
                    }
                } catch (Exception e) {
                    LogKit.debug("QR专用检测策略" + (i+1) + "失败: " + e.getMessage());
                }
            }

        } catch (Exception e) {
            LogKit.debug("QR码专用检测异常: " + e.getMessage());
        }
    }

    /**
     * 检查是否存在重复的二维码
     */
    private boolean isDuplicateQRCode(OcrResponse response, String qrText) {
        if (response.getElements() == null) {
            return false;
        }

        for (Map<String, Object> element : response.getElements()) {
            String elementType = String.valueOf(element.get("elementType"));
            String content = (String) element.get("content");

            if ("7".equals(elementType) && qrText.equals(content)) {
                return true; // 找到重复的二维码
            }
        }
        return false;
    }

    /**
     * 检查是否存在重复的条形码
     */
    private boolean isDuplicateBarcode(OcrResponse response, String barcodeText) {
        if (response.getElements() == null) {
            return false;
        }

        for (Map<String, Object> element : response.getElements()) {
            String elementType = String.valueOf(element.get("elementType"));
            String content = (String) element.get("content");

            if ("2".equals(elementType) && barcodeText.equals(content)) {
                return true; // 找到重复的条形码
            }
        }
        return false;
    }

    /**
     * 从ZXing Result中计算条码边界
     *
     * @param result ZXing识别结果
     * @return [x, y, width, height]
     */
    private int[] calculateZXingBounds(Result result) {
        try {
            // 获取ZXing的ResultPoint数组
            com.google.zxing.ResultPoint[] resultPoints = result.getResultPoints();

            if (resultPoints != null && resultPoints.length >= 2) {
                // 计算所有定位点的边界
                float minX = Float.MAX_VALUE;
                float minY = Float.MAX_VALUE;
                float maxX = Float.MIN_VALUE;
                float maxY = Float.MIN_VALUE;

                for (com.google.zxing.ResultPoint point : resultPoints) {
                    if (point != null) {
                        float x = point.getX();
                        float y = point.getY();
                        minX = Math.min(minX, x);
                        minY = Math.min(minY, y);
                        maxX = Math.max(maxX, x);
                        maxY = Math.max(maxY, y);
                    }
                }

                // 计算边界框，并添加一些边距
                int x = Math.max(0, (int)minX - 5);
                int y = Math.max(0, (int)minY - 5);
                int width = (int)(maxX - minX) + 10;
                int height = (int)(maxY - minY) + 10;

                LogKit.info("从" + resultPoints.length + "个定位点计算条码边界: [" + x + "," + y + "," + width + "," + height + "]");
                return new int[]{x, y, width, height};

            } else {
                LogKit.warn("ZXing未提供定位点信息，使用默认估算位置");
            }

        } catch (Exception e) {
            LogKit.warn("计算ZXing条码边界失败: " + e.getMessage());
        }

        // 降级方案：返回估算的中心位置
        return new int[]{100, 100, 100, 50}; // 估算位置，比之前的(10,10,50,50)更合理
    }

    /**
     * 判断是否为二维码类型
     * 根据TextIn官方文档的sub_type定义
     */
    private boolean isQRCode(String subType) {
        if (subType == null) {
            return false;
        }

        // 严格按照TextIn官方sub_type定义：当type为image时，sub_type包括：
        // stamp(印章)、chart(图表)、qrcode(二维码)、barcode(条形码)
        return "qrcode".equals(subType);
    }

    /**
     * 判断ZXing格式是否为二维码
     * 根据ZXing支持的格式，包含所有二维码类型
     */
    private boolean isQRCodeFormat(String format) {
        if (format == null) {
            return false;
        }

        // ZXing支持的所有二维码格式
        return format.equals("QR_CODE") ||           // 标准QR码
               format.equals("DATA_MATRIX") ||       // Data Matrix
               format.equals("AZTEC") ||             // Aztec码
               format.equals("PDF_417") ||           // PDF417
               format.equals("MAXICODE") ||          // MaxiCode
               format.equals("RSS_14") ||            // RSS-14 (GS1 DataBar)
               format.equals("RSS_EXPANDED");        // RSS Expanded
    }

    /**
     * 映射条码类型到XPrinter格式
     */
    private String mapBarcodeType(String textInType) {
        if (textInType == null) {
            return "4"; // 默认CODE_128
        }

        switch (textInType.toLowerCase()) {
            case "code128":
            case "code_128":
                return "4";
            case "code39":
            case "code_39":
                return "1";
            case "ean13":
            case "ean_13":
                return "8";
            case "ean8":
            case "ean_8":
                return "9";
            default:
                return "4"; // 默认CODE_128
        }
    }

    /**
     * 映射ZXing条码类型到XPrinter格式
     */
    private String mapZXingBarcodeType(String zxingFormat) {
        if (zxingFormat == null) {
            return "4";
        }

        switch (zxingFormat) {
            case "CODE_128":
                return "4";
            case "CODE_39":
                return "1";
            case "EAN_13":
                return "8";
            case "EAN_8":
                return "9";
            default:
                return "4";
        }
    }

    /**
     * 获取图片格式
     */
    private String getImageFormat(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "unknown";
        }

        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0 && lastDot < fileName.length() - 1) {
            return fileName.substring(lastDot + 1).toLowerCase();
        }

        return "unknown";
    }

    /**
     * 基于TextIn数据智能判断文本是否为粗体
     *
     * @param ocrItem TextIn的raw_ocr项
     * @return 是否为粗体
     */
    @SuppressWarnings("unchecked")
    private boolean isTextBold(Map<String, Object> ocrItem) {
        try {
            // 优先级1: 检查sub_type字段
            String subType = (String) ocrItem.get("sub_type");
            if ("text_title".equals(subType) || "catalog".equals(subType)) {
                LogKit.info("基于sub_type判断为粗体: " + subType);
                return true;
            }

            // 优先级2: 检查outline_level字段（标题级别）
            Object outlineLevel = ocrItem.get("outline_level");
            if (outlineLevel instanceof Integer && ((Integer) outlineLevel) > 0) {
                LogKit.info("基于outline_level判断为粗体: " + outlineLevel);
                return true;
            }

            // 优先级3: 检查tags数组中是否包含特殊标记
            List<String> tags = (List<String>) ocrItem.get("tags");
            if (tags != null && (tags.contains("title") || tags.contains("heading"))) {
                LogKit.info("基于tags判断为粗体: " + tags);
                return true;
            }

        } catch (Exception e) {
            LogKit.warn("判断粗体样式时出错: " + e.getMessage());
        }

        return false; // 默认非粗体
    }

    /**
     * 降级算法：基于TextIn数据简单判断文本斜体（仅在没有char_positions时使用）
     * 注意：这个方法主要用作降级，因为handwritten不等于italic
     *
     * @param ocrItem TextIn的raw_ocr项
     * @return 是否为斜体
     */
    @SuppressWarnings("unchecked")
    private boolean isTextItalicFallback(Map<String, Object> ocrItem) {
        try {
            // 注意：这只是降级策略，实际上handwritten ≠ italic
            // 真正的斜体应该通过几何分析判断

            // 检查是否明确标记为斜体相关的标签
            List<String> tags = (List<String>) ocrItem.get("tags");
            if (tags != null && (tags.contains("italic") || tags.contains("slanted"))) {
                LogKit.info("基于tags降级判断为斜体: " + tags);
                return true;
            }

            // 其他情况默认为非斜体，避免基于handwritten的错误判断

        } catch (Exception e) {
            LogKit.warn("降级判断斜体样式时出错: " + e.getMessage());
        }

        return false; // 默认非斜体
    }

    /**
     * 表格结构数据类
     */
    private static class TableStructure {
        public int rows;
        public int cols;
        public List<Map<String, Object>> cells;

        public TableStructure(int rows, int cols, List<Map<String, Object>> cells) {
            this.rows = rows;
            this.cols = cols;
            this.cells = cells;
        }
    }

    /**
     * 表格尺寸数据类
     */
    private static class TableDimensions {
        public List<Integer> rowHeights;
        public List<Integer> columnWidths;

        public TableDimensions(List<Integer> rowHeights, List<Integer> columnWidths) {
            this.rowHeights = rowHeights;
            this.columnWidths = columnWidths;
        }
    }

    /**
     * 待补充内容的条码信息
     */
    private static class PendingBarcode {
        public String type;  // "qrcode" 或 "barcode"
        public int x;
        public int y;
        public int width;
        public int height;
        public int elementIndex;  // 在response.elements中的索引
        public String imageUrl;  // TextIn返回的条码图片URL

        public PendingBarcode(String type, int x, int y, int width, int height, int elementIndex) {
            this.type = type;
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.elementIndex = elementIndex;
        }

        public PendingBarcode(String type, int x, int y, int width, int height, int elementIndex, String imageUrl) {
            this(type, x, y, width, height, elementIndex);
            this.imageUrl = imageUrl;
        }
    }

    /**
     * 解析TextIn的表格数据，转换为XPrinter格式
     *
     * @param textinCells TextIn返回的cells数组
     * @return 解析后的表格结构
     */
    @SuppressWarnings("unchecked")
    private TableStructure parseTextInTable(List<Map<String, Object>> textinCells) {
        if (textinCells == null || textinCells.isEmpty()) {
            return new TableStructure(1, 1, new ArrayList<>());
        }

        // 1. 计算表格的实际行列数
        int maxRow = 0, maxCol = 0;
        for (Map<String, Object> textinCell : textinCells) {
            try {
                Object rowObj = textinCell.get("row");
                Object colObj = textinCell.get("col");

                if (rowObj instanceof Integer && colObj instanceof Integer) {
                    int row = (Integer) rowObj;
                    int col = (Integer) colObj;

                    // 考虑跨行跨列的情况
                    Object rowSpanObj = textinCell.get("row_span");
                    Object colSpanObj = textinCell.get("col_span");

                    int rowSpan = (rowSpanObj instanceof Integer) ? (Integer) rowSpanObj : 1;
                    int colSpan = (colSpanObj instanceof Integer) ? (Integer) colSpanObj : 1;

                    maxRow = Math.max(maxRow, row + rowSpan - 1);
                    maxCol = Math.max(maxCol, col + colSpan - 1);
                }
            } catch (Exception e) {
                LogKit.warn("解析单元格行列信息时出错: " + e.getMessage());
            }
        }

        // 表格行列数至少为1
        int tableRows = Math.max(maxRow + 1, 1);
        int tableCols = Math.max(maxCol + 1, 1);

        // 2. 转换单元格数据
        List<Map<String, Object>> convertedCells = new ArrayList<>();
        for (Map<String, Object> textinCell : textinCells) {
            try {
                Map<String, Object> xprinterCell = convertTextInCell(textinCell);
                if (xprinterCell != null) {
                    convertedCells.add(xprinterCell);
                }
            } catch (Exception e) {
                LogKit.warn("转换单元格数据时出错: " + e.getMessage());
            }
        }

        return new TableStructure(tableRows, tableCols, convertedCells);
    }

    /**
     * 从structured数组中提取表格的行高列宽信息
     * TextIn在pages[0].structured[0]中提供了columns_width和rows_height数据
     *
     * @return 表格尺寸信息，如果未找到则返回null
     */
    @SuppressWarnings("unchecked")
    private TableDimensions extractTableDimensions() {
        try {
            if (currentStructuredList == null || currentStructuredList.isEmpty()) {
                LogKit.warn("structured数组为空，无法提取表格尺寸信息");
                return null;
            }

            // 查找表格类型的structured元素
            for (Map<String, Object> structuredItem : currentStructuredList) {
                String type = (String) structuredItem.get("type");
                if ("table".equals(type)) {
                    // 提取行高信息
                    List<Integer> rowsHeight = (List<Integer>) structuredItem.get("rows_height");
                    // 提取列宽信息
                    List<Integer> columnWidths = (List<Integer>) structuredItem.get("columns_width");

                    if (rowsHeight != null || columnWidths != null) {
                        LogKit.info("成功提取表格尺寸信息: 行高数组=" + (rowsHeight != null ? rowsHeight.size() + "项" : "null") +
                                   ", 列宽数组=" + (columnWidths != null ? columnWidths.size() + "项" : "null"));
                        return new TableDimensions(rowsHeight, columnWidths);
                    } else {
                        LogKit.warn("structured表格元素中没有尺寸信息");
                    }
                }
            }

            LogKit.warn("在structured数组中未找到表格类型的元素");
            return null;

        } catch (Exception e) {
            LogKit.error("提取表格尺寸信息时出错: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从指定的structured表格元素中提取尺寸信息
     * 直接从传入的structured表格对象中获取行高列宽信息
     *
     * @param structuredTableItem structured数组中的表格元素
     * @return 表格尺寸信息，如果未找到则返回null
     */
    @SuppressWarnings("unchecked")
    private TableDimensions extractTableDimensionsFromStructured(Map<String, Object> structuredTableItem) {
        try {
            // 直接从当前structured表格元素提取尺寸信息
            List<Integer> rowsHeight = (List<Integer>) structuredTableItem.get("rows_height");
            List<Integer> columnWidths = (List<Integer>) structuredTableItem.get("columns_width");

            if (rowsHeight != null || columnWidths != null) {
                LogKit.info("从structured表格元素中提取到尺寸信息: 行高数组=" + (rowsHeight != null ? rowsHeight.size() + "项" : "null") +
                           ", 列宽数组=" + (columnWidths != null ? columnWidths.size() + "项" : "null"));
                return new TableDimensions(rowsHeight, columnWidths);
            } else {
                LogKit.warn("当前structured表格元素中没有尺寸信息");
                return null;
            }

        } catch (Exception e) {
            LogKit.error("从structured表格元素提取尺寸信息时出错: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换单个TextIn单元格为XPrinter格式
     *
     * @param textinCell TextIn的单元格数据
     * @return XPrinter格式的单元格数据
     */
    private Map<String, Object> convertTextInCell(Map<String, Object> textinCell) {
        if (textinCell == null) {
            return null;
        }

        Map<String, Object> xprinterCell = new HashMap<>();

        try {
            // 基础位置信息
            Object rowObj = textinCell.get("row");
            Object colObj = textinCell.get("col");

            xprinterCell.put("row", rowObj instanceof Integer ? rowObj : 0);
            xprinterCell.put("col", colObj instanceof Integer ? colObj : 0);

            // 单元格内容
            String rawCellText = (String) textinCell.get("text");
            // 先清理markdown格式符号
            String cellText = cleanMarkdownFormatting(rawCellText);
            // 处理单元格内容中的换行符和img标签
            String processedCellText = processCellContentLineBreaks(cellText);
            xprinterCell.put("content", processedCellText != null ? processedCellText : "");

            // 跨行跨列信息（如果存在）
            Object rowSpanObj = textinCell.get("row_span");
            Object colSpanObj = textinCell.get("col_span");

            if (rowSpanObj instanceof Integer && ((Integer) rowSpanObj) > 1) {
                xprinterCell.put("rowSpan", rowSpanObj);
            }
            if (colSpanObj instanceof Integer && ((Integer) colSpanObj) > 1) {
                xprinterCell.put("colSpan", colSpanObj);
            }

            LogKit.debug("转换单元格: (" + xprinterCell.get("row") + "," +
                        xprinterCell.get("col") + ") = '" + xprinterCell.get("content") + "'");

        } catch (Exception e) {
            LogKit.warn("转换单元格详细信息时出错: " + e.getMessage());
            // 提供默认值
            xprinterCell.put("row", 0);
            xprinterCell.put("col", 0);
            xprinterCell.put("content", "");
        }

        return xprinterCell;
    }

    /**
     * 从TextIn数据中提取旋转角度
     *
     * @param ocrItem TextIn的raw_ocr项
     * @return 旋转角度（度）
     */
    private double extractRotationAngle(Map<String, Object> ocrItem) {
        try {
            Object angleObj = ocrItem.get("angle");
            if (angleObj instanceof Number) {
                double angle = ((Number) angleObj).doubleValue();
                LogKit.debug("提取旋转角度: " + angle);
                return angle;
            }
        } catch (Exception e) {
            LogKit.warn("提取旋转角度时出错: " + e.getMessage());
        }
        return 0.0; // 默认无旋转
    }

    /**
     * 从TextIn数据中提取文字方向
     *
     * @param ocrItem TextIn的raw_ocr项
     * @return 文字方向
     */
    private String extractTextDirection(Map<String, Object> ocrItem) {
        try {
            Object directionObj = ocrItem.get("direction");
            if (directionObj instanceof Integer) {
                int direction = (Integer) directionObj;
                // TextIn的direction: 0=水平, 1=垂直, 其他值待确认
                switch (direction) {
                    case 0:
                        return "horizontal";
                    case 1:
                        return "vertical";
                    default:
                        LogKit.debug("未知的文字方向值: " + direction);
                        return "horizontal";
                }
            }
        } catch (Exception e) {
            LogKit.warn("提取文字方向时出错: " + e.getMessage());
        }
        return "horizontal"; // 默认水平方向
    }

    /**
     * 从TextIn数据中提取识别置信度
     *
     * @param ocrItem TextIn的raw_ocr项
     * @return 识别置信度（0-1）
     */
    private double extractConfidence(Map<String, Object> ocrItem) {
        try {
            Object scoreObj = ocrItem.get("score");
            if (scoreObj instanceof Number) {
                double score = ((Number) scoreObj).doubleValue();
                // 确保置信度在0-1范围内
                if (score < 0.0) score = 0.0;
                if (score > 1.0) score = 1.0;
                LogKit.debug("提取识别置信度: " + score);
                return score;
            }
        } catch (Exception e) {
            LogKit.warn("提取识别置信度时出错: " + e.getMessage());
        }
        return 1.0; // 默认高置信度
    }

    /**
     * 从TextIn数据中提取手写体标识
     *
     * @param ocrItem TextIn的raw_ocr项
     * @return 是否为手写体
     */
    private boolean extractHandwritten(Map<String, Object> ocrItem) {
        try {
            Object handwrittenObj = ocrItem.get("handwritten");
            if (handwrittenObj instanceof Integer) {
                int handwritten = (Integer) handwrittenObj;
                boolean isHandwritten = handwritten == 1;
                LogKit.debug("提取手写体标识: " + isHandwritten);
                return isHandwritten;
            }
        } catch (Exception e) {
            LogKit.warn("提取手写体标识时出错: " + e.getMessage());
        }
        return false; // 默认非手写体
    }

    /**
     * 查找条形码对应的文本内容
     * 分析TextIn返回的数据结构，条形码内容可能在以下位置：
     * 1. 条形码图像元素后面的line元素中
     * 2. 与条形码位置相近的纯数字文本元素中
     *
     * @param contentList 内容列表
     * @param barcodeImageItem 条形码图像元素
     * @return 条形码文本内容
     */
    private String findBarcodeContent(List<Map<String, Object>> contentList, Map<String, Object> barcodeImageItem) {
        try {
            Integer barcodeId = (Integer) barcodeImageItem.get("id");
            if (barcodeId == null) {
                LogKit.warn("条形码元素缺少id字段");
                return null;
            }

            LogKit.info("查找条形码内容，条形码图像id: " + barcodeId);
            List<Integer> barcodePos = (List<Integer>) barcodeImageItem.get("pos");

            // 策略1: 查找紧跟在条形码后面的文本元素
            for (Map<String, Object> item : contentList) {
                Integer itemId = (Integer) item.get("id");
                String itemType = (String) item.get("type");
                String itemText = (String) item.get("text");

                if (itemId != null && itemId > barcodeId && "line".equals(itemType) &&
                    !StrKit.isBlank(itemText) && isLikelyBarcodeText(itemText)) {
                    LogKit.info("找到条形码内容(策略1): " + itemText + " (id=" + itemId + ")");
                    return itemText;
                }
            }

            // 策略2: 查找与条形码位置相近的数字文本
            if (barcodePos != null && barcodePos.size() >= 8) {
                int barcodeY = Math.min(Math.min(barcodePos.get(1), barcodePos.get(3)),
                                       Math.min(barcodePos.get(5), barcodePos.get(7)));
                int barcodeBottomY = Math.max(Math.max(barcodePos.get(1), barcodePos.get(3)),
                                             Math.max(barcodePos.get(5), barcodePos.get(7)));

                for (Map<String, Object> item : contentList) {
                    String itemType = (String) item.get("type");
                    String itemText = (String) item.get("text");
                    List<Integer> itemPos = (List<Integer>) item.get("pos");

                    if ("line".equals(itemType) && !StrKit.isBlank(itemText) &&
                        isLikelyBarcodeText(itemText) && itemPos != null && itemPos.size() >= 8) {

                        int itemY = Math.min(Math.min(itemPos.get(1), itemPos.get(3)),
                                            Math.min(itemPos.get(5), itemPos.get(7)));

                        // 检查文本是否在条形码下方附近（允许一定的容差）
                        if (itemY >= barcodeBottomY - 50 && itemY <= barcodeBottomY + 100) {
                            LogKit.info("找到条形码内容(策略2): " + itemText + " (位置相近)");
                            return itemText;
                        }
                    }
                }
            }

            LogKit.warn("未找到条形码对应的文本内容");
            return null;

        } catch (Exception e) {
            LogKit.error("查找条形码内容时出错: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 判断文本是否像条形码内容（主要是数字）
     */
    private boolean isLikelyBarcodeText(String text) {
        if (StrKit.isBlank(text)) {
            return false;
        }
        // 条形码通常是纯数字，长度在8-20位之间
        return text.matches("\\d{8,20}");
    }

    /**
     * 查找二维码对应的文本内容
     * 目前TextIn对二维码的解码支持有限，主要依赖ZXing补充
     * 重要：只从TextIn明确返回的data字段获取内容，不猜测
     *
     * @param contentList 内容列表
     * @param qrCodeImageItem 二维码图像元素
     * @return 二维码文本内容
     */
    private String findQrCodeContent(List<Map<String, Object>> contentList, Map<String, Object> qrCodeImageItem) {
        try {
            // 只检查TextIn是否在data字段中提供了二维码内容
            Map<String, Object> data = (Map<String, Object>) qrCodeImageItem.get("data");
            if (data != null) {
                String qrContent = (String) data.get("content");
                if (!StrKit.isBlank(qrContent)) {
                    LogKit.info("从data字段找到二维码内容: " + qrContent);
                    return qrContent;
                }
            }

            // 不再猜测附近的文本是二维码内容
            LogKit.info("TextIn未提供二维码解码内容，将依赖ZXing补充识别");
            return null;

        } catch (Exception e) {
            LogKit.error("查找二维码内容时出错: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 判断文本是否像二维码内容
     */
    private boolean isLikelyQrCodeText(String text) {
        if (StrKit.isBlank(text)) {
            return false;
        }
        // 二维码内容通常是URL、文本或包含特殊字符的字符串
        return text.length() > 5 && (text.contains("http") || text.contains("://") ||
                                     text.contains("@") || text.length() > 10);
    }

    /**
     * 建立content元素到structured信息的映射
     * TextIn的structured数组包含content数组的结构化信息（如sub_type）
     *
     * @param contentList content数组
     * @param structuredList structured数组
     * @return 从content元素ID到structured信息的映射
     */
    @SuppressWarnings("unchecked")
    private Map<Integer, Map<String, Object>> buildContentToStructuredMapping(
            List<Map<String, Object>> contentList,
            List<Map<String, Object>> structuredList) {

        Map<Integer, Map<String, Object>> mapping = new HashMap<>();

        if (structuredList == null || structuredList.isEmpty()) {
            LogKit.warn("structured数组为空，无法建立映射");
            return mapping;
        }

        LogKit.info("开始建立content到structured的映射，structured数量: " + structuredList.size());

        for (Map<String, Object> structuredItem : structuredList) {
            try {
                // structured元素中的content字段包含了对应的content数组元素的ID列表
                List<Integer> contentIds = (List<Integer>) structuredItem.get("content");

                if (contentIds != null && !contentIds.isEmpty()) {
                    // 为每个关联的content元素建立映射
                    for (Integer contentId : contentIds) {
                        mapping.put(contentId, structuredItem);
                        LogKit.debug("建立映射: content[" + contentId + "] -> structured[" +
                                   structuredItem.get("sub_type") + "]");
                    }
                }

            } catch (Exception e) {
                LogKit.warn("处理structured元素时出错: " + e.getMessage());
            }
        }

        LogKit.info("映射建立完成，映射数量: " + mapping.size());
        return mapping;
    }

    /**
     * 获取被structured元素使用的content索引集合
     * 这些索引对应的content不应该作为独立文本元素处理，避免重复
     *
     * @param structuredList structured数组
     * @return 被使用的content索引集合
     */
    @SuppressWarnings("unchecked")
    private Set<Integer> getContentIndicesUsedByStructured(List<Map<String, Object>> structuredList) {
        Set<Integer> usedIndices = new HashSet<>();

        if (structuredList == null || structuredList.isEmpty()) {
            return usedIndices;
        }

        for (Map<String, Object> structuredItem : structuredList) {
            String type = (String) structuredItem.get("type");
            LogKit.info("分析structured元素使用的content索引: type=" + type);

            if ("table".equals(type)) {
                // 表格元素：分析cells中引用的content索引
                List<Map<String, Object>> cells = (List<Map<String, Object>>) structuredItem.get("cells");
                if (cells != null) {
                    for (Map<String, Object> cell : cells) {
                        Set<Integer> cellIndices = extractContentIndicesFromCell(cell);
                        usedIndices.addAll(cellIndices);
                    }
                }
            }
            // 未来可以扩展处理其他structured类型：图表、段落等
        }

        LogKit.info("structured元素总共使用的content索引: " + usedIndices);
        return usedIndices;
    }

    /**
     * 从表格单元格中提取引用的content索引
     */
    @SuppressWarnings("unchecked")
    private Set<Integer> extractContentIndicesFromCell(Map<String, Object> cell) {
        Set<Integer> indices = new HashSet<>();

        try {
            List<Map<String, Object>> contentRefs = (List<Map<String, Object>>) cell.get("content");
            if (contentRefs != null) {
                for (Map<String, Object> contentRef : contentRefs) {
                    if ("textblock".equals(contentRef.get("type"))) {
                        List<Integer> contentIndices = (List<Integer>) contentRef.get("content");
                        if (contentIndices != null) {
                            indices.addAll(contentIndices);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LogKit.warn("提取单元格content索引时出错: " + e.getMessage());
        }

        return indices;
    }

    /**
     * 【新架构辅助方法】根据sub_type判断文本是否为粗体
     */
    private boolean isTextBoldBySubType(String subType) {
        if (subType == null) {
            return false;
        }

        // 根据TextIn官方文档的sub_type定义，标题类型通常为粗体
        return subType.equals("text_title") || subType.equals("image_title") ||
               subType.equals("table_title") || subType.equals("catalog");
    }

    /**
     * 【新架构辅助方法】从content元素中提取斜体信息
     */
    @SuppressWarnings("unchecked")
    private boolean extractItalicFromContent(Map<String, Object> contentItem) {
        try {
            // 优先使用char_positions进行几何分析
            List<List<Integer>> charPositions = (List<List<Integer>>) contentItem.get("char_positions");
            String text = (String) contentItem.get("text");

            if (charPositions != null && !charPositions.isEmpty() && text != null) {
                return CoordinateUtils.calculateItalicFromGeometry(text, charPositions);
            }

            // 降级策略：检查tags中是否有斜体标记
            List<String> tags = (List<String>) contentItem.get("tags");
            if (tags != null && (tags.contains("italic") || tags.contains("slanted"))) {
                return true;
            }

        } catch (Exception e) {
            LogKit.warn("提取斜体信息时出错: " + e.getMessage());
        }

        return false;
    }

    /**
     * 【新架构辅助方法】从content元素计算字符宽度
     */
    @SuppressWarnings("unchecked")
    private double calculateCharWidthFromContent(Map<String, Object> contentItem, String text, int width) {
        try {
            List<List<Integer>> charPositions = (List<List<Integer>>) contentItem.get("char_positions");

            if (charPositions != null && !charPositions.isEmpty() && text != null) {
                return CoordinateUtils.calculateSmartCharWidth(text, charPositions);
            }

            // 降级策略：简单计算
            return text != null && text.length() > 0 ? (double) width / text.length() : 0.0;

        } catch (Exception e) {
            LogKit.warn("计算字符宽度时出错: " + e.getMessage());
            return text != null && text.length() > 0 ? (double) width / text.length() : 0.0;
        }
    }

    /**
     * 【新架构辅助方法】为detail元素查找二维码内容
     * 重要：只从TextIn明确返回的data字段获取内容，不猜测
     */
    @SuppressWarnings("unchecked")
    private String findQrCodeContentForDetail(Map<String, Object> detailItem) {
        try {
            // 检查detail中的text字段（通常为空）
            String directText = (String) detailItem.get("text");
            if (!StrKit.isBlank(directText)) {
                // 只有当text明显是二维码内容时才使用（如URL或纯数字串）
                if (directText.startsWith("http") || directText.matches("\\d{6,}")) {
                    return directText;
                }
            }

            // 通过content索引查找对应的content元素
            Integer contentIndex = (Integer) detailItem.get("content");
            if (contentIndex != null && currentContentList != null &&
                contentIndex >= 0 && contentIndex < currentContentList.size()) {

                Map<String, Object> contentItem = currentContentList.get(contentIndex);

                // 只检查content元素中的data字段，这是TextIn解码的二维码内容
                Map<String, Object> data = (Map<String, Object>) contentItem.get("data");
                if (data != null) {
                    String qrContent = (String) data.get("content");
                    if (!StrKit.isBlank(qrContent)) {
                        LogKit.info("从TextIn data字段获取二维码内容: " + qrContent);
                        return qrContent;
                    }
                }
            }

            // 不再猜测附近的文本是二维码内容
            LogKit.info("TextIn未提供二维码解码内容，将依赖ZXing补充");

        } catch (Exception e) {
            LogKit.warn("查找二维码内容时出错: " + e.getMessage());
        }

        return null;
    }

    /**
     * 【新架构辅助方法】为detail元素查找条形码内容
     * 重要：只从TextIn明确返回的data字段获取内容，不猜测
     */
    @SuppressWarnings("unchecked")
    private String findBarcodeContentForDetail(Map<String, Object> detailItem) {
        try {
            // 检查detail中的text字段（通常为空）
            String directText = (String) detailItem.get("text");
            if (!StrKit.isBlank(directText)) {
                // 只有当text是纯数字串时才使用（条形码通常是纯数字）
                if (directText.matches("\\d{8,20}")) {
                    return directText;
                }
            }

            // 通过content索引查找对应的content元素
            Integer contentIndex = (Integer) detailItem.get("content");
            if (contentIndex != null && currentContentList != null &&
                contentIndex >= 0 && contentIndex < currentContentList.size()) {

                Map<String, Object> contentItem = currentContentList.get(contentIndex);

                // 只检查content元素中的data字段
                Map<String, Object> data = (Map<String, Object>) contentItem.get("data");
                if (data != null) {
                    String barcodeContent = (String) data.get("content");
                    if (!StrKit.isBlank(barcodeContent)) {
                        LogKit.info("从TextIn data字段获取条形码内容: " + barcodeContent);
                        return barcodeContent;
                    }
                }
            }

            // 不再猜测附近的文本是条形码内容
            LogKit.info("TextIn未提供条形码解码内容，将依赖ZXing补充");

        } catch (Exception e) {
            LogKit.warn("查找条形码内容时出错: " + e.getMessage());
        }

        return null;
    }

    /**
     * 【新架构辅助方法】查找与给定元素相关的文本内容
     * @deprecated 不应该猜测条码内容，这个方法会导致错误的内容关联
     */
    @Deprecated
    @SuppressWarnings("unchecked")
    private String findRelatedTextContent(Map<String, Object> imageItem, String imageType) {
        try {
            if (currentContentList == null) {
                return null;
            }

            Integer imageId = (Integer) imageItem.get("id");
            List<Integer> imagePos = (List<Integer>) imageItem.get("pos");

            if (imageId == null || imagePos == null || imagePos.size() < 8) {
                return null;
            }

            // 策略1：查找id紧跟在后面的文本元素
            for (Map<String, Object> item : currentContentList) {
                Integer itemId = (Integer) item.get("id");
                String itemType = (String) item.get("type");
                String itemText = (String) item.get("text");

                if (itemId != null && itemId > imageId && "line".equals(itemType) && !StrKit.isBlank(itemText)) {
                    if (("qrcode".equals(imageType) && isLikelyQrCodeText(itemText)) ||
                        ("barcode".equals(imageType) && isLikelyBarcodeText(itemText))) {
                        return itemText;
                    }
                }
            }

            // 策略2：查找位置相近的文本元素
            int imageBottomY = Math.max(Math.max(imagePos.get(1), imagePos.get(3)),
                                       Math.max(imagePos.get(5), imagePos.get(7)));

            for (Map<String, Object> item : currentContentList) {
                String itemType = (String) item.get("type");
                String itemText = (String) item.get("text");
                List<Integer> itemPos = (List<Integer>) item.get("pos");

                if ("line".equals(itemType) && !StrKit.isBlank(itemText) &&
                    itemPos != null && itemPos.size() >= 8) {

                    int itemY = Math.min(Math.min(itemPos.get(1), itemPos.get(3)),
                                        Math.min(itemPos.get(5), itemPos.get(7)));

                    // 检查文本是否在图像下方附近
                    if (itemY >= imageBottomY - 50 && itemY <= imageBottomY + 100) {
                        if (("qrcode".equals(imageType) && isLikelyQrCodeText(itemText)) ||
                            ("barcode".equals(imageType) && isLikelyBarcodeText(itemText))) {
                            return itemText;
                        }
                    }
                }
            }

        } catch (Exception e) {
            LogKit.warn("查找相关文本内容时出错: " + e.getMessage());
        }

        return null;
    }

    /**
     * 【新架构辅助方法】解析detail中的表格单元格
     */
    @SuppressWarnings("unchecked")
    private TableStructure parseDetailTableCells(List<Map<String, Object>> detailCells) {
        if (detailCells == null || detailCells.isEmpty()) {
            return new TableStructure(1, 1, new ArrayList<>());
        }

        // 计算表格的实际行列数
        int maxRow = 0, maxCol = 0;
        for (Map<String, Object> cell : detailCells) {
            try {
                Object rowObj = cell.get("row");
                Object colObj = cell.get("col");

                if (rowObj instanceof Integer && colObj instanceof Integer) {
                    int row = (Integer) rowObj;
                    int col = (Integer) colObj;

                    // 考虑跨行跨列的情况
                    Object rowSpanObj = cell.get("row_span");
                    Object colSpanObj = cell.get("col_span");

                    int rowSpan = (rowSpanObj instanceof Integer) ? (Integer) rowSpanObj : 1;
                    int colSpan = (colSpanObj instanceof Integer) ? (Integer) colSpanObj : 1;

                    maxRow = Math.max(maxRow, row + rowSpan - 1);
                    maxCol = Math.max(maxCol, col + colSpan - 1);
                }
            } catch (Exception e) {
                LogKit.warn("解析单元格行列信息时出错: " + e.getMessage());
            }
        }

        // 表格行列数至少为1
        int tableRows = Math.max(maxRow + 1, 1);
        int tableCols = Math.max(maxCol + 1, 1);

        // 转换单元格数据
        List<Map<String, Object>> convertedCells = new ArrayList<>();
        for (Map<String, Object> cell : detailCells) {
            try {
                Map<String, Object> convertedCell = convertDetailTableCell(cell);
                if (convertedCell != null) {
                    convertedCells.add(convertedCell);
                }
            } catch (Exception e) {
                LogKit.warn("转换detail单元格数据时出错: " + e.getMessage());
            }
        }

        return new TableStructure(tableRows, tableCols, convertedCells);
    }

    /**
     * 【新架构辅助方法】转换detail中的单个表格单元格
     * 增强版：为单元格添加charWidth等样式信息，并检测单元格内的条码
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> convertDetailTableCell(Map<String, Object> detailCell) {
        if (detailCell == null) {
            return null;
        }

        Map<String, Object> convertedCell = new HashMap<>();

        try {
            // 基本位置信息
            Object rowObj = detailCell.get("row");
            Object colObj = detailCell.get("col");

            convertedCell.put("row", rowObj instanceof Integer ? rowObj : 0);
            convertedCell.put("col", colObj instanceof Integer ? colObj : 0);

            // 单元格内容
            String rawCellText = (String) detailCell.get("text");
            // 清理markdown格式符号
            String cellText = cleanMarkdownFormatting(rawCellText);
            // 处理单元格内容中的换行符：将<br>替换为\n，便于APP端显示
            String processedCellText = processCellContentLineBreaks(cellText);
            convertedCell.put("content", processedCellText != null ? processedCellText : "");

            // 跨行跨列信息
            Object rowSpanObj = detailCell.get("row_span");
            Object colSpanObj = detailCell.get("col_span");

            if (rowSpanObj instanceof Integer && ((Integer) rowSpanObj) > 1) {
                convertedCell.put("rowSpan", rowSpanObj);
            }
            if (colSpanObj instanceof Integer && ((Integer) colSpanObj) > 1) {
                convertedCell.put("colSpan", colSpanObj);
            }

            // 【新增】从content数组获取单元格的详细样式信息
            addCellStyleInformation(convertedCell, detailCell, cellText);

        } catch (Exception e) {
            LogKit.warn("转换detail单元格详细信息时出错: " + e.getMessage());
            // 提供默认值
            convertedCell.put("row", 0);
            convertedCell.put("col", 0);
            convertedCell.put("content", "");
        }

        return convertedCell;
    }

    /**
     * 【新架构辅助方法】处理structured数组中可能的额外结构化元素
     * 主要用于处理detail数组中没有包含但structured中存在的元素
     */
    @SuppressWarnings("unchecked")
    private void processAdditionalStructuredElements(OcrResponse response) {
        try {
            if (currentStructuredList == null || currentStructuredList.isEmpty()) {
                return;
            }

            LogKit.info("检查structured数组中的额外结构化元素");

            // 获取已处理的元素信息，避免重复处理
            // 这里可以添加逻辑来处理detail数组中未包含但structured中存在的表格等元素
            // 目前版本先保持简单，后续根据实际需要扩展

        } catch (Exception e) {
            LogKit.error("处理额外结构化元素时出错: " + e.getMessage(), e);
        }
    }

    /**
     * 【新增方法】为表格单元格添加详细样式信息（charWidth、粗体、斜体等）
     * 通过在structured中cells中查找对应的content索引，然后从content数组获取详细信息
     */
    @SuppressWarnings("unchecked")
    private void addCellStyleInformation(Map<String, Object> convertedCell, Map<String, Object> detailCell, String cellText) {
        try {
            if (StrKit.isBlank(cellText) || currentStructuredList == null || currentContentList == null) {
                // 设置默认样式信息
                setDefaultCellStyle(convertedCell, cellText);
                return;
            }

            // 查找structured数组中对应的表格元素
            Map<String, Object> structuredTable = findStructuredTableForCell(detailCell);
            if (structuredTable == null) {
                setDefaultCellStyle(convertedCell, cellText);
                return;
            }

            // 获取structured表格中的cells数组
            List<Map<String, Object>> structuredCells = (List<Map<String, Object>>) structuredTable.get("cells");
            if (structuredCells == null) {
                setDefaultCellStyle(convertedCell, cellText);
                return;
            }

            // 查找对应的structured单元格
            Map<String, Object> matchingStructuredCell = findMatchingStructuredCell(
                structuredCells, (Integer) convertedCell.get("row"), (Integer) convertedCell.get("col"));

            if (matchingStructuredCell != null) {
                // 从structured单元格中获取content索引
                List<Integer> contentIndices = extractContentIndicesFromStructuredCell(matchingStructuredCell);

                if (!contentIndices.isEmpty()) {
                    // 处理多个content索引的情况（如"批号"被分成"批"和"号"两个元素）
                    if (contentIndices.size() == 1) {
                        // 单个content元素的情况
                        Integer contentIndex = contentIndices.get(0);
                        if (contentIndex >= 0 && contentIndex < currentContentList.size()) {
                            Map<String, Object> contentItem = currentContentList.get(contentIndex);
                            extractAndSetCellStyle(convertedCell, contentItem, cellText);
                        } else {
                            LogKit.warn("content索引超出范围: " + contentIndex + ", currentContentList大小: " + currentContentList.size());
                            setDefaultCellStyle(convertedCell, cellText);
                        }
                    } else {
                        // 多个content元素的情况，需要合并位置信息
                        LogKit.debug("单元格包含多个content元素: " + contentIndices.size() + "个，将合并位置信息");
                        extractAndSetCellStyleFromMultipleContents(convertedCell, contentIndices, cellText);
                    }
                } else {
                    LogKit.debug("未找到content索引，使用默认样式");
                    setDefaultCellStyle(convertedCell, cellText);
                }
            } else {
                LogKit.debug("未找到匹配的structured单元格，使用默认样式");
                setDefaultCellStyle(convertedCell, cellText);
            }

        } catch (Exception e) {
            LogKit.warn("添加单元格样式信息时出错: " + e.getMessage());
            setDefaultCellStyle(convertedCell, cellText);
        }
    }

    /**
     * 查找detail单元格对应的structured表格元素
     * 通过位置信息匹配正确的表格
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> findStructuredTableForCell(Map<String, Object> detailCell) {
        if (currentStructuredList == null) {
            return null;
        }

        // 获取detail单元格的位置信息
        List<Integer> detailPos = (List<Integer>) detailCell.get("position");
        if (detailPos == null || detailPos.size() < 8) {
            LogKit.warn("detail单元格位置信息无效: " + detailPos);
            return null;
        }

        // 计算detail单元格的边界
        int detailMinX = Math.min(Math.min(detailPos.get(0), detailPos.get(2)), Math.min(detailPos.get(4), detailPos.get(6)));
        int detailMinY = Math.min(Math.min(detailPos.get(1), detailPos.get(3)), Math.min(detailPos.get(5), detailPos.get(7)));
        int detailMaxX = Math.max(Math.max(detailPos.get(0), detailPos.get(2)), Math.max(detailPos.get(4), detailPos.get(6)));
        int detailMaxY = Math.max(Math.max(detailPos.get(1), detailPos.get(3)), Math.max(detailPos.get(5), detailPos.get(7)));

        LogKit.info("查找detail单元格对应的structured表格，detail位置: [" + detailMinX + "," + detailMinY + "," + detailMaxX + "," + detailMaxY + "]");

        for (Map<String, Object> structuredItem : currentStructuredList) {
            String type = (String) structuredItem.get("type");
            if ("table".equals(type)) {
                List<Integer> structuredPos = (List<Integer>) structuredItem.get("pos");
                if (structuredPos != null && structuredPos.size() >= 8) {
                    // 计算structured表格的边界
                    int structuredMinX = Math.min(Math.min(structuredPos.get(0), structuredPos.get(2)), Math.min(structuredPos.get(4), structuredPos.get(6)));
                    int structuredMinY = Math.min(Math.min(structuredPos.get(1), structuredPos.get(3)), Math.min(structuredPos.get(5), structuredPos.get(7)));
                    int structuredMaxX = Math.max(Math.max(structuredPos.get(0), structuredPos.get(2)), Math.max(structuredPos.get(4), structuredPos.get(6)));
                    int structuredMaxY = Math.max(Math.max(structuredPos.get(1), structuredPos.get(3)), Math.max(structuredPos.get(5), structuredPos.get(7)));

                    LogKit.info("检查structured表格位置: [" + structuredMinX + "," + structuredMinY + "," + structuredMaxX + "," + structuredMaxY + "]");

                    // 检查detail单元格是否在structured表格范围内（允许一定的误差）
                    int tolerance = 10; // 10像素的容差
                    if (detailMinX >= structuredMinX - tolerance && detailMinY >= structuredMinY - tolerance &&
                        detailMaxX <= structuredMaxX + tolerance && detailMaxY <= structuredMaxY + tolerance) {
                        LogKit.info("找到匹配的structured表格: " + structuredItem.get("id"));
                        return structuredItem;
                    }
                }
            }
        }

        LogKit.warn("未找到匹配的structured表格，使用第一个表格作为降级方案");
        // 降级方案：返回第一个表格
        for (Map<String, Object> structuredItem : currentStructuredList) {
            String type = (String) structuredItem.get("type");
            if ("table".equals(type)) {
                return structuredItem;
            }
        }

        return null;
    }

    /**
     * 在structured cells中查找匹配的单元格
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> findMatchingStructuredCell(List<Map<String, Object>> structuredCells, int targetRow, int targetCol) {
        for (Map<String, Object> cell : structuredCells) {
            Object rowObj = cell.get("row");
            Object colObj = cell.get("col");

            if (rowObj instanceof Integer && colObj instanceof Integer) {
                int row = (Integer) rowObj;
                int col = (Integer) colObj;

                if (row == targetRow && col == targetCol) {
                    return cell;
                }
            }
        }
        return null;
    }

    /**
     * 从structured单元格中提取content索引
     */
    @SuppressWarnings("unchecked")
    private List<Integer> extractContentIndicesFromStructuredCell(Map<String, Object> structuredCell) {
        List<Integer> indices = new ArrayList<>();

        try {
            List<Map<String, Object>> contentRefs = (List<Map<String, Object>>) structuredCell.get("content");

            if (contentRefs != null) {
                for (Map<String, Object> contentRef : contentRefs) {
                    String type = (String) contentRef.get("type");

                    if ("textblock".equals(type)) {
                        List<Integer> contentIndices = (List<Integer>) contentRef.get("content");

                        if (contentIndices != null) {
                            indices.addAll(contentIndices);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LogKit.warn("提取structured单元格content索引时出错: " + e.getMessage());
        }

        return indices;
    }

    /**
     * 从content元素中提取并设置单元格的样式信息
     * 增强版：添加alignment、bold、italic、width、height等属性
     */
    @SuppressWarnings("unchecked")
    private void extractAndSetCellStyle(Map<String, Object> convertedCell, Map<String, Object> contentItem, String cellText) {
        try {
            // 获取文本位置信息（content中的pos是文本块的实际位置）
            List<Integer> textPos = (List<Integer>) contentItem.get("pos");

            // 从单元格的detailCell或structuredCell获取单元格边界
            Map<String, Object> cellBounds = getCellBounds(convertedCell);

            // 计算charWidth
            List<List<Integer>> charPositions = (List<List<Integer>>) contentItem.get("char_positions");
            double charWidth;
            if (charPositions != null && !charPositions.isEmpty() && cellText != null && !cellText.isEmpty()) {
                charWidth = CoordinateUtils.calculateSmartCharWidth(cellText, charPositions);
                LogKit.debug("表格单元格智能计算charWidth: " + charWidth + " for text: " + cellText);
            } else {
                // 降级策略：从位置信息计算
                if (textPos != null && textPos.size() >= 8 && cellText != null && !cellText.isEmpty()) {
                    int textWidth = Math.max(Math.max(textPos.get(0), textPos.get(2)), Math.max(textPos.get(4), textPos.get(6))) -
                                   Math.min(Math.min(textPos.get(0), textPos.get(2)), Math.min(textPos.get(4), textPos.get(6)));
                    charWidth = (double) textWidth / cellText.length();
                } else {
                    charWidth = 12.0; // 默认字符宽度
                }
            }
            convertedCell.put("charWidth", charWidth);

            // 【新增】计算文本块的width和height（文本块的bbox尺寸）
            if (textPos != null && textPos.size() >= 8) {
                int textWidth = Math.max(Math.max(textPos.get(0), textPos.get(2)), Math.max(textPos.get(4), textPos.get(6))) -
                               Math.min(Math.min(textPos.get(0), textPos.get(2)), Math.min(textPos.get(4), textPos.get(6)));
                int textHeight = Math.max(Math.max(textPos.get(1), textPos.get(3)), Math.max(textPos.get(5), textPos.get(7))) -
                                Math.min(Math.min(textPos.get(1), textPos.get(3)), Math.min(textPos.get(5), textPos.get(7)));

                convertedCell.put("width", textWidth);
                convertedCell.put("height", textHeight);

                LogKit.debug("文本块尺寸: " + textWidth + "x" + textHeight + " for text: '" + cellText + "'");
            } else {
                convertedCell.put("width", 0);
                convertedCell.put("height", 0);
                LogKit.debug("textPos无效，设置默认尺寸0x0 for text: '" + cellText + "'");
            }

            // 【新增】计算文本对齐方式（基于文本在单元格中的相对位置）
            String alignment = calculateTextAlignment(textPos, cellBounds, cellText);
            convertedCell.put("alignment", alignment);

            // 【新增】检测是否包含图片内容
            boolean hasImage = detectImageContentInCell(cellText);
            convertedCell.put("hasImage", hasImage);
            if (hasImage) {
                LogKit.info("检测到单元格包含图片内容: " + cellText);
            }

            // 判断是否为粗体
            boolean isBold = isTextBold(contentItem);
            convertedCell.put("bold", isBold);

            // 判断是否为斜体
            boolean isItalic = extractItalicFromContent(contentItem);
            convertedCell.put("italic", isItalic);

            // 提取其他样式信息
            double rotationAngle = extractRotationAngle(contentItem);
            convertedCell.put("rotationAngle", rotationAngle);

            String textDirection = extractTextDirection(contentItem);
            convertedCell.put("textDirection", textDirection);

            double confidence = extractConfidence(contentItem);
            convertedCell.put("confidence", confidence);

            boolean isHandwritten = extractHandwritten(contentItem);
            convertedCell.put("isHandwritten", isHandwritten);

            LogKit.debug("表格单元格完整样式: '" + cellText + "' - charWidth=" + charWidth +
                        ", bold=" + isBold + ", italic=" + isItalic + ", alignment=" + alignment +
                        ", size=" + convertedCell.get("width") + "x" + convertedCell.get("height"));

        } catch (Exception e) {
            LogKit.warn("提取单元格样式信息时出错: " + e.getMessage());
            setDefaultCellStyle(convertedCell, cellText);
        }
    }

    /**
     * 设置默认的单元格样式信息
     * 增强版：添加新属性的默认值
     */
    private void setDefaultCellStyle(Map<String, Object> convertedCell, String cellText) {
        double defaultCharWidth = (cellText != null && !cellText.isEmpty()) ?
                                 Math.max(12.0, 100.0 / cellText.length()) : 12.0;

        convertedCell.put("charWidth", defaultCharWidth);

        // 设置默认属性值
        convertedCell.put("alignment", "left"); // 默认左对齐
        convertedCell.put("bold", false);
        convertedCell.put("italic", false);
        convertedCell.put("width", 0);
        convertedCell.put("height", 0);
        convertedCell.put("rotationAngle", 0.0);
        convertedCell.put("textDirection", "horizontal");
        convertedCell.put("confidence", 1.0);
        convertedCell.put("isHandwritten", false);

        // 【新增】检测图片内容
        boolean hasImage = detectImageContentInCell(cellText);
        convertedCell.put("hasImage", hasImage);
        if (hasImage) {
            LogKit.info("默认样式中检测到单元格包含图片内容: " + cellText);
        }

        LogKit.debug("使用默认单元格样式: '" + cellText + "' - charWidth=" + defaultCharWidth);
    }

    /**
     * 【新增方法】从多个content元素中提取并合并样式信息
     * 当表格单元格的文本被分成多个content元素时（如"批号"被分成"批"和"号"），
     * 需要合并所有content元素的位置信息来计算正确的尺寸
     *
     * @param convertedCell 转换后的单元格对象
     * @param contentIndices content索引列表
     * @param cellText 单元格文本内容
     */
    @SuppressWarnings("unchecked")
    private void extractAndSetCellStyleFromMultipleContents(Map<String, Object> convertedCell,
                                                           List<Integer> contentIndices,
                                                           String cellText) {
        try {
            if (contentIndices == null || contentIndices.isEmpty()) {
                setDefaultCellStyle(convertedCell, cellText);
                return;
            }

            LogKit.info("合并多个content元素的位置信息，数量: " + contentIndices.size() + "，文本: '" + cellText + "'");

            // 收集所有content元素的位置信息
            List<List<Integer>> allPositions = new ArrayList<>();
            List<List<List<Integer>>> allCharPositions = new ArrayList<>();
            boolean hasBold = false;
            boolean hasItalic = false;
            double avgConfidence = 0.0;
            int validContentCount = 0;

            for (Integer contentIndex : contentIndices) {
                if (contentIndex >= 0 && contentIndex < currentContentList.size()) {
                    Map<String, Object> contentItem = currentContentList.get(contentIndex);

                    // 获取位置信息
                    List<Integer> textPos = (List<Integer>) contentItem.get("pos");
                    if (textPos != null && textPos.size() >= 8) {
                        allPositions.add(textPos);
                        LogKit.debug("content[" + contentIndex + "] 位置: " + textPos);
                    }

                    // 获取字符位置信息
                    List<List<Integer>> charPositions = (List<List<Integer>>) contentItem.get("char_positions");
                    if (charPositions != null && !charPositions.isEmpty()) {
                        allCharPositions.add(charPositions);
                    }

                    // 检测样式信息（任一文本块为粗体则整体为粗体）
                    if (isTextBold(contentItem)) {
                        hasBold = true;
                    }

                    // 检测斜体
                    if (extractItalicFromContent(contentItem)) {
                        hasItalic = true;
                    }

                    // 累计置信度
                    avgConfidence += extractConfidence(contentItem);
                    validContentCount++;

                } else {
                    LogKit.warn("content索引超出范围: " + contentIndex);
                }
            }

            if (allPositions.isEmpty()) {
                LogKit.warn("没有找到有效的位置信息，使用默认样式");
                setDefaultCellStyle(convertedCell, cellText);
                return;
            }

            // 计算合并后的边界框
            int minX = Integer.MAX_VALUE;
            int minY = Integer.MAX_VALUE;
            int maxX = Integer.MIN_VALUE;
            int maxY = Integer.MIN_VALUE;

            for (List<Integer> pos : allPositions) {
                minX = Math.min(minX, Math.min(Math.min(pos.get(0), pos.get(2)), Math.min(pos.get(4), pos.get(6))));
                minY = Math.min(minY, Math.min(Math.min(pos.get(1), pos.get(3)), Math.min(pos.get(5), pos.get(7))));
                maxX = Math.max(maxX, Math.max(Math.max(pos.get(0), pos.get(2)), Math.max(pos.get(4), pos.get(6))));
                maxY = Math.max(maxY, Math.max(Math.max(pos.get(1), pos.get(3)), Math.max(pos.get(5), pos.get(7))));
            }

            int totalWidth = maxX - minX;
            int totalHeight = maxY - minY;

            LogKit.info("合并后的边界框: [" + minX + "," + minY + "," + totalWidth + "," + totalHeight + "]");

            // 设置合并后的尺寸
            convertedCell.put("width", totalWidth);
            convertedCell.put("height", totalHeight);

            // 计算字符宽度
            double charWidth;
            if (!allCharPositions.isEmpty() && cellText != null && !cellText.isEmpty()) {
                // 合并所有字符位置信息
                List<List<Integer>> mergedCharPositions = new ArrayList<>();
                for (List<List<Integer>> charPosList : allCharPositions) {
                    mergedCharPositions.addAll(charPosList);
                }

                // 使用合并后的字符位置信息计算
                charWidth = CoordinateUtils.calculateSmartCharWidth(cellText, mergedCharPositions);
                LogKit.debug("基于合并的字符位置计算charWidth: " + charWidth);
            } else {
                // 降级策略：使用合并后的总宽度计算
                charWidth = (cellText != null && !cellText.isEmpty()) ?
                           (double) totalWidth / cellText.length() : 12.0;
                LogKit.debug("使用合并宽度计算charWidth: " + charWidth);
            }
            convertedCell.put("charWidth", charWidth);

            // 设置样式信息
            convertedCell.put("bold", hasBold);
            convertedCell.put("italic", hasItalic);

            // 计算平均置信度
            if (validContentCount > 0) {
                avgConfidence /= validContentCount;
            } else {
                avgConfidence = 1.0;
            }
            convertedCell.put("confidence", avgConfidence);

            // 获取单元格边界并计算对齐方式
            Map<String, Object> cellBounds = getCellBounds(convertedCell);
            List<Integer> mergedPos = new ArrayList<>();
            mergedPos.add(minX);
            mergedPos.add(minY);
            mergedPos.add(maxX);
            mergedPos.add(minY);
            mergedPos.add(maxX);
            mergedPos.add(maxY);
            mergedPos.add(minX);
            mergedPos.add(maxY);
            String alignment = calculateTextAlignment(mergedPos, cellBounds, cellText);
            convertedCell.put("alignment", alignment);

            // 设置其他默认属性
            convertedCell.put("rotationAngle", 0.0);
            convertedCell.put("textDirection", "horizontal");
            convertedCell.put("isHandwritten", false);

            // 检测图片内容
            boolean hasImage = detectImageContentInCell(cellText);
            convertedCell.put("hasImage", hasImage);

            LogKit.info("多content合并完成: 文本='" + cellText + "', 尺寸=" + totalWidth + "x" + totalHeight +
                       ", charWidth=" + charWidth + ", bold=" + hasBold + ", italic=" + hasItalic +
                       ", alignment=" + alignment + ", confidence=" + String.format("%.2f", avgConfidence));

        } catch (Exception e) {
            LogKit.error("合并多个content元素样式时出错: " + e.getMessage(), e);
            setDefaultCellStyle(convertedCell, cellText);
        }
    }

    /**
     * 【新增方法】获取单元格的边界信息
     * 从detail或structured数据中获取单元格的实际边界框
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> getCellBounds(Map<String, Object> convertedCell) {
        Map<String, Object> bounds = new HashMap<>();

        try {
            Integer row = (Integer) convertedCell.get("row");
            Integer col = (Integer) convertedCell.get("col");

            if (row == null || col == null || currentStructuredList == null) {
                return bounds; // 返回空的bounds
            }

            // 查找structured表格元素
            Map<String, Object> structuredTable = findStructuredTableForCell(null);
            if (structuredTable != null) {
                List<Map<String, Object>> structuredCells = (List<Map<String, Object>>) structuredTable.get("cells");
                if (structuredCells != null) {
                    // 查找匹配的structured单元格
                    Map<String, Object> matchingCell = findMatchingStructuredCell(structuredCells, row, col);
                    if (matchingCell != null) {
                        List<Integer> pos = (List<Integer>) matchingCell.get("pos");
                        if (pos != null && pos.size() >= 8) {
                            int minX = Math.min(Math.min(pos.get(0), pos.get(2)), Math.min(pos.get(4), pos.get(6)));
                            int minY = Math.min(Math.min(pos.get(1), pos.get(3)), Math.min(pos.get(5), pos.get(7)));
                            int maxX = Math.max(Math.max(pos.get(0), pos.get(2)), Math.max(pos.get(4), pos.get(6)));
                            int maxY = Math.max(Math.max(pos.get(1), pos.get(3)), Math.max(pos.get(5), pos.get(7)));

                            bounds.put("minX", minX);
                            bounds.put("minY", minY);
                            bounds.put("maxX", maxX);
                            bounds.put("maxY", maxY);
                            bounds.put("width", maxX - minX);
                            bounds.put("height", maxY - minY);

                            LogKit.debug("单元格边界: (" + row + "," + col + ") = [" + minX + "," + minY + "," + (maxX-minX) + "," + (maxY-minY) + "]");
                        }
                    }
                }
            }
        } catch (Exception e) {
            LogKit.warn("获取单元格边界信息时出错: " + e.getMessage());
        }

        return bounds;
    }

    /**
     * 【新增方法】计算文本在单元格内的对齐方式
     * 基于文本位置相对于单元格边界的位置进行智能推断
     */
    @SuppressWarnings("unchecked")
    private String calculateTextAlignment(List<Integer> textPos, Map<String, Object> cellBounds, String cellText) {
        try {
            if (textPos == null || textPos.size() < 8 || cellBounds.isEmpty() || StrKit.isBlank(cellText)) {
                return "left"; // 默认左对齐
            }

            // 计算文本边界
            int textMinX = Math.min(Math.min(textPos.get(0), textPos.get(2)), Math.min(textPos.get(4), textPos.get(6)));
            int textMaxX = Math.max(Math.max(textPos.get(0), textPos.get(2)), Math.max(textPos.get(4), textPos.get(6)));
            int textCenterX = (textMinX + textMaxX) / 2;
            int textWidth = textMaxX - textMinX;

            // 获取单元格边界
            Integer cellMinX = (Integer) cellBounds.get("minX");
            Integer cellMaxX = (Integer) cellBounds.get("maxX");
            Integer cellWidth = (Integer) cellBounds.get("width");

            if (cellMinX == null || cellMaxX == null || cellWidth == null || cellWidth <= 0) {
                return "left";
            }

            int cellCenterX = (cellMinX + cellMaxX) / 2;

            // 计算文本相对于单元格的位置比例
            double textRelativePosition = (double)(textCenterX - cellMinX) / cellWidth;

            // 计算文本距离左边界和右边界的距离
            int leftMargin = textMinX - cellMinX;
            int rightMargin = cellMaxX - textMaxX;

            LogKit.debug("对齐分析: 文本中心=" + textCenterX + ", 单元格中心=" + cellCenterX +
                        ", 相对位置=" + String.format("%.2f", textRelativePosition) +
                        ", 左边距=" + leftMargin + ", 右边距=" + rightMargin);

            // 智能判断对齐方式
            if (textRelativePosition <= 0.25) {
                // 文本偏左（相对位置在前25%）
                return "left";
            } else if (textRelativePosition >= 0.75) {
                // 文本偏右（相对位置在后25%）
                return "right";
            } else if (textRelativePosition >= 0.35 && textRelativePosition <= 0.65) {
                // 文本居中（相对位置在35%-65%之间）
                // 额外检查：左右边距是否相近（差距在文本宽度的20%以内）
                int marginDiff = Math.abs(leftMargin - rightMargin);
                if (marginDiff <= textWidth * 0.2) {
                    return "center";
                }
            }

            // 备用判断：基于边距比较
            if (Math.abs(leftMargin - rightMargin) <= Math.max(5, textWidth * 0.1)) {
                return "center"; // 左右边距相近，判断为居中
            } else if (leftMargin < rightMargin) {
                return "left"; // 左边距小，判断为左对齐
            } else {
                return "right"; // 右边距小，判断为右对齐
            }

        } catch (Exception e) {
            LogKit.warn("计算文本对齐方式时出错: " + e.getMessage());
            return "left";
        }
    }

    /**
     * 【新增方法】检测单元格内容是否包含图片
     * 根据TextIn返回的图片标记（HTML img标签）来判断单元格是否包含图片内容
     *
     * @param cellText 单元格文本内容
     * @return 是否包含图片
     */
    private boolean detectImageContentInCell(String cellText) {
        if (StrKit.isBlank(cellText)) {
            return false;
        }

        try {
            // 检测TextIn返回的图片标记格式
            // 格式1: <img src="https://web-api.textin.com/ocr_image/external/xxxxxx.jpg">
            // 格式2: <img src="https://web-api.textin.com/ocr_image/external/xxxxxx.png">
            // 格式3: 其他可能的img标签格式

            // 使用正则表达式检测HTML img标签
            String imgPattern = "(?i)<img[^>]+src\\s*=\\s*[\"']([^\"']*)[\"'][^>]*>";

            if (cellText.matches(".*" + imgPattern + ".*")) {
                LogKit.info("检测到HTML img标签格式的图片内容");
                return true;
            }

            // 检测常见的图片URL格式（防止img标签被解析或变形的情况）
            String urlPattern = "https?://[^\\s]+\\.(jpg|jpeg|png|gif|bmp|webp)";
            if (cellText.matches(".*" + urlPattern + ".*")) {
                LogKit.info("检测到图片URL格式的内容");
                return true;
            }

            // 检测TextIn特有的图片服务域名
            if (cellText.contains("web-api.textin.com/ocr_image/external/")) {
                LogKit.info("检测到TextIn图片服务URL");
                return true;
            }

            // 检测其他可能的图片标识
            String lowerCaseText = cellText.toLowerCase().trim();
            if (lowerCaseText.startsWith("<img") ||
                lowerCaseText.contains("ocr_image") ||
                lowerCaseText.contains("image/") ||
                lowerCaseText.matches(".*\\.(jpg|jpeg|png|gif|bmp|webp).*")) {
                LogKit.info("检测到其他格式的图片标识");
                return true;
            }

        } catch (Exception e) {
            LogKit.warn("检测图片内容时出错: " + e.getMessage());
        }

        return false;
    }

    /**
     * 从detail元素中提取图片URL
     * TextIn可能在多个字段中返回图片链接
     *
     * @param detailItem detail数组中的图片元素
     * @return 图片URL或base64数据
     */
    @SuppressWarnings("unchecked")
    private String extractImageUrl(Map<String, Object> detailItem) {
        try {
            // 方法1：检查detail中是否直接包含图片URL
            String directUrl = (String) detailItem.get("image_url");
            if (!StrKit.isBlank(directUrl)) {
                return directUrl;
            }

            // 方法2：检查data字段中的图片信息
            Map<String, Object> data = (Map<String, Object>) detailItem.get("data");
            if (data != null) {
                String dataUrl = (String) data.get("image_url");
                if (!StrKit.isBlank(dataUrl)) {
                    return dataUrl;
                }

                String base64Data = (String) data.get("image_base64");
                if (!StrKit.isBlank(base64Data)) {
                    return base64Data;
                }
            }

            // 方法3：通过content索引查找对应的content元素
            Integer contentIndex = (Integer) detailItem.get("content");
            if (contentIndex != null && currentContentList != null &&
                contentIndex >= 0 && contentIndex < currentContentList.size()) {

                Map<String, Object> contentItem = currentContentList.get(contentIndex);
                return findImageUrlFromContent(contentItem);
            }

        } catch (Exception e) {
            LogKit.warn("提取图片URL时出错: " + e.getMessage());
        }

        return null;
    }

    /**
     * 从content元素中查找图片URL
     *
     * @param contentItem content数组中的元素
     * @return 图片URL或base64数据
     */
    @SuppressWarnings("unchecked")
    private String findImageUrlFromContent(Map<String, Object> contentItem) {
        try {
            // 检查data字段
            Map<String, Object> data = (Map<String, Object>) contentItem.get("data");
            if (data != null) {
                String imageUrl = (String) data.get("image_url");
                if (!StrKit.isBlank(imageUrl)) {
                    LogKit.info("从content.data.image_url获取到图片链接: " + imageUrl);
                    return imageUrl;
                }

                String base64Data = (String) data.get("image_base64");
                if (!StrKit.isBlank(base64Data)) {
                    LogKit.info("从content.data.image_base64获取到图片数据");
                    return "data:image/png;base64," + base64Data;
                }

                // TextIn可能使用的其他字段名
                String imgSrc = (String) data.get("src");
                if (!StrKit.isBlank(imgSrc)) {
                    LogKit.info("从content.data.src获取到图片链接: " + imgSrc);
                    return imgSrc;
                }

            }

            // 检查直接字段
            String directUrl = (String) contentItem.get("image_url");
            if (!StrKit.isBlank(directUrl)) {
                LogKit.info("从content.image_url获取到图片链接: " + directUrl);
                return directUrl;
            }

            // 检查text字段中是否包含HTML img标签
            String text = (String) contentItem.get("text");
            if (!StrKit.isBlank(text) && text.contains("<img")) {
                String extractedUrl = extractUrlFromImgTag(text);
                if (!StrKit.isBlank(extractedUrl)) {
                    LogKit.info("从content.text的img标签中提取到图片链接: " + extractedUrl);
                    return extractedUrl;
                }
            }

        } catch (Exception e) {
            LogKit.warn("从content查找图片URL时出错: " + e.getMessage());
        }

        return null;
    }

    /**
     * 从HTML img标签中提取图片URL
     *
     * @param htmlText 包含img标签的HTML文本
     * @return 提取的图片URL
     */
    private String extractUrlFromImgTag(String htmlText) {
        try {
            // 使用正则表达式提取src属性值
            String pattern = "(?i)<img[^>]+src\\s*=\\s*[\"']([^\"']*)[\"'][^>]*>";
            java.util.regex.Pattern regex = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher matcher = regex.matcher(htmlText);

            if (matcher.find()) {
                String url = matcher.group(1);
                LogKit.info("从img标签提取URL: " + url);
                return url;
            }

        } catch (Exception e) {
            LogKit.warn("从img标签提取URL时出错: " + e.getMessage());
        }

        return null;
    }

    /**
     * 从detail元素中提取图片识别置信度
     *
     * @param detailItem detail数组中的图片元素
     * @return 识别置信度
     */
    private double extractImageConfidence(Map<String, Object> detailItem) {
        try {
            // 检查confidence字段
            Object confidenceObj = detailItem.get("confidence");
            if (confidenceObj instanceof Number) {
                double confidence = ((Number) confidenceObj).doubleValue();
                return Math.max(0.0, Math.min(1.0, confidence));
            }

            // 检查score字段
            Object scoreObj = detailItem.get("score");
            if (scoreObj instanceof Number) {
                double score = ((Number) scoreObj).doubleValue();
                return Math.max(0.0, Math.min(1.0, score));
            }

        } catch (Exception e) {
            LogKit.warn("提取图片置信度时出错: " + e.getMessage());
        }

        return 0.8; // 默认置信度
    }

    /**
     * 【调试增强】分析content数组中的图像元素
     * 帮助理解TextIn返回了哪些图像数据以及它们的处理情况
     */
    @SuppressWarnings("unchecked")
    private void analyzeImageElementsInContent(List<Map<String, Object>> contentList) {
        try {
            LogKit.info("🔍 分析content数组中的图像元素");

            if (contentList == null || contentList.isEmpty()) {
                LogKit.info("❌ content数组为空");
                return;
            }

            int totalElements = contentList.size();
            int imageElements = 0;
            java.util.Map<String, Integer> subTypeCount = new java.util.HashMap<>();

            for (int i = 0; i < contentList.size(); i++) {
                Map<String, Object> item = contentList.get(i);
                String type = (String) item.get("type");

                if ("image".equals(type)) {
                    imageElements++;
                    String subType = (String) item.get("sub_type");
                    Integer id = (Integer) item.get("id");

                    LogKit.info("📸 发现图像元素[" + i + "] id=" + id + ", sub_type=" + subType);

                    // 统计sub_type
                    subTypeCount.put(subType != null ? subType : "null",
                                   subTypeCount.getOrDefault(subType, 0) + 1);

                    // 检查是否有data字段（可能包含图片URL）
                    Map<String, Object> data = (Map<String, Object>) item.get("data");
                    if (data != null) {
                        LogKit.info("  📋 包含data字段，keys: " + data.keySet());
                        if (data.containsKey("image_url")) {
                            LogKit.info("  🔗 包含image_url: " + data.get("image_url"));
                        }
                    } else {
                        LogKit.info("  ⚠️ 没有data字段");
                    }

                    // 检查位置信息
                    List<Integer> pos = (List<Integer>) item.get("pos");
                    if (pos != null && pos.size() >= 8) {
                        int minX = Math.min(Math.min(pos.get(0), pos.get(2)), Math.min(pos.get(4), pos.get(6)));
                        int minY = Math.min(Math.min(pos.get(1), pos.get(3)), Math.min(pos.get(5), pos.get(7)));
                        int maxX = Math.max(Math.max(pos.get(0), pos.get(2)), Math.max(pos.get(4), pos.get(6)));
                        int maxY = Math.max(Math.max(pos.get(1), pos.get(3)), Math.max(pos.get(5), pos.get(7)));
                        LogKit.info("  📍 位置: (" + minX + "," + minY + ") 尺寸: " + (maxX-minX) + "x" + (maxY-minY));
                    }
                }
            }

            LogKit.info("📊 图像元素统计:");
            LogKit.info("  总元素数量: " + totalElements);
            LogKit.info("  图像元素数量: " + imageElements);
            LogKit.info("  sub_type分布: " + subTypeCount);

            if (imageElements == 0) {
                LogKit.info("❌ 没有发现任何type=image的元素");
                LogKit.info("   这说明TextIn API没有识别出图像内容，修复代码无法处理");
            } else {
                LogKit.info("✅ 发现 " + imageElements + " 个图像元素");

                // 分析哪些会被处理为图片元素
                int willBeProcessed = 0;
                for (Map.Entry<String, Integer> entry : subTypeCount.entrySet()) {
                    String subType = entry.getKey();
                    int count = entry.getValue();

                    if (!"qrcode".equals(subType) && !"barcode".equals(subType)) {
                        willBeProcessed += count;
                        LogKit.info("  🎯 " + count + "个" + subType + "类型将被处理为图片元素");
                    } else {
                        LogKit.info("  ✅ " + count + "个" + subType + "类型将被处理为条码/二维码");
                    }
                }

                if (willBeProcessed == 0) {
                    LogKit.info("⚠️ 所有图像元素都是qrcode/barcode，不会产生elementType=8的图片元素");
                    LogKit.info("   这解释了为什么接口没有返回elementType=8的元素");
                } else {
                    LogKit.info("🎉 预计将产生 " + willBeProcessed + " 个elementType=8的图片元素");
                }
            }

        } catch (Exception e) {
            LogKit.error("分析图像元素时出错: " + e.getMessage(), e);
        }
    }

    /**
     * 映射图片子类型到标准类型
     *
     * @param subType TextIn返回的sub_type
     * @return 标准化的图片类型
     */
    private String mapImageSubType(String subType) {
        if (subType == null) {
            return "graphic";
        }

        switch (subType.toLowerCase()) {
            case "stamp":
                return "stamp";      // 印章
            case "chart":
                return "chart";      // 图表
            case "logo":
                return "logo";       // 品牌标识
            case "icon":
                return "icon";       // 图标
            case "graphic":
                return "graphic";    // 通用图形
            default:
                LogKit.info("未知的图片sub_type: " + subType + "，映射为generic");
                return "generic";    // 通用类型
        }
    }

    /**
     * 处理表格单元格内容中的换行符
     * 将HTML的<br>标签替换为APP支持的\n换行符，并移除img标签
     *
     * @param cellText 原始单元格文本内容
     * @return 处理后的单元格内容
     */
    private String processCellContentLineBreaks(String cellText) {
        if (StrKit.isBlank(cellText)) {
            return cellText;
        }

        try {
            // 先移除img标签
            String processedText = removeImageTags(cellText);

            // 替换各种形式的<br>标签为\n
            processedText = processedText
                // 标准的<br>标签（不区分大小写）
                .replaceAll("(?i)<br\\s*/?>", "\n")
                // 自闭合的<br/>标签
                .replaceAll("(?i)<br\\s*/>", "\n")
                // 带属性的<br>标签
                .replaceAll("(?i)<br[^>]*>", "\n")
                // 处理可能的HTML实体编码的<br>
                .replaceAll("&lt;br&gt;", "\n")
                .replaceAll("&lt;br/&gt;", "\n")
                .replaceAll("&lt;br /&gt;", "\n");

            // 如果内容发生了变化，记录日志
            if (!cellText.equals(processedText)) {
                LogKit.info("表格单元格内容换行符处理: '" + cellText + "' -> '" + processedText + "'");
            }

            return processedText;

        } catch (Exception e) {
            LogKit.warn("处理单元格内容换行符时出错: " + e.getMessage());
            return cellText; // 出错时返回原内容
        }
    }

    /**
     * 移除文本中的HTML img标签
     * TextIn API 可能在表格单元格内容中返回img标签，需要移除这些标签
     *
     * @param text 原始文本
     * @return 移除img标签后的文本
     */
    private String removeImageTags(String text) {
        if (StrKit.isBlank(text)) {
            return text;
        }

        try {
            String cleanedText = text;

            // 移除所有img标签，包括自闭合和普通标签
            // 匹配各种形式的img标签：<img ...>, <img .../>, <IMG ...>等
            cleanedText = cleanedText.replaceAll("(?i)<img[^>]*>", "");

            // 如果文本发生了变化，记录日志
            if (!text.equals(cleanedText)) {
                LogKit.info("移除img标签: '" + text + "' -> '" + cleanedText + "'");
            }

            return cleanedText.trim(); // 移除可能产生的多余空格

        } catch (Exception e) {
            LogKit.warn("移除img标签时出错: " + e.getMessage());
            return text; // 出错时返回原文本
        }
    }

    /**
     * 判断表格是否实际上是边框
     * 边框的特征：1行1列且单元格内容为空
     *
     * @param tableStructure 表格结构
     * @return 是否为边框
     */
    private boolean isTableActuallyBorder(TableStructure tableStructure) {
        if (tableStructure == null) {
            return false;
        }

        // 检查是否为1行1列
        if (tableStructure.rows != 1 || tableStructure.cols != 1) {
            return false;
        }

        // 检查单元格内容是否为空
        if (tableStructure.cells == null || tableStructure.cells.isEmpty()) {
            return true; // 没有单元格数据，认为是边框
        }

        // 检查第一个（也是唯一的）单元格内容
        if (tableStructure.cells.size() == 1) {
            Map<String, Object> cell = tableStructure.cells.get(0);
            String content = (String) cell.get("content");
            // 内容为空或只有空白字符，认为是边框
            return StrKit.isBlank(content);
        }

        return false;
    }

    /**
     * 【新增方法】检测表格单元格内的条码并生成独立的条码组件
     * 解决表格单元格内一维码/二维码无法独立识别的问题
     *
     * @param cells detail表格的cells数组
     * @param tableX 表格的X坐标
     * @param tableY 表格的Y坐标
     * @param response OCR响应对象
     */
    @SuppressWarnings("unchecked")
    private void detectBarcodesInTableCells(List<Map<String, Object>> cells, int tableX, int tableY, OcrResponse response) {
        if (cells == null || cells.isEmpty()) {
            LogKit.info("表格没有cells数据，跳过单元格条码检测");
            return;
        }

        LogKit.info("🔍 开始检测表格单元格内的条码，单元格数量: " + cells.size());

        int barcodeCount = 0;
        int qrcodeCount = 0;

        for (int i = 0; i < cells.size(); i++) {
            try {
                Map<String, Object> cell = cells.get(i);

                // 获取单元格基本信息
                Integer row = (Integer) cell.get("row");
                Integer col = (Integer) cell.get("col");
                String cellText = (String) cell.get("text");
                List<Integer> position = (List<Integer>) cell.get("position");

                LogKit.debug("检测单元格[" + i + "] (" + row + "," + col + ") 文本: '" + cellText + "'");

                if (position == null || position.size() < 8) {
                    LogKit.debug("单元格位置信息无效，跳过");
                    continue;
                }

                // 计算单元格位置
                int cellMinX = Math.min(Math.min(position.get(0), position.get(2)), Math.min(position.get(4), position.get(6)));
                int cellMinY = Math.min(Math.min(position.get(1), position.get(3)), Math.min(position.get(5), position.get(7)));
                int cellMaxX = Math.max(Math.max(position.get(0), position.get(2)), Math.max(position.get(4), position.get(6)));
                int cellMaxY = Math.max(Math.max(position.get(1), position.get(3)), Math.max(position.get(5), position.get(7)));
                int cellWidth = cellMaxX - cellMinX;
                int cellHeight = cellMaxY - cellMinY;

                // 检测单元格内容中是否包含图片（可能是条码）
                boolean hasImageContent = detectImageContentInCell(cellText);

                if (hasImageContent) {
                    LogKit.info("📸 单元格(" + row + "," + col + ")检测到图片内容，尝试识别条码");

                    // 尝试从TextIn的structured数据中查找对应的图像元素信息
                    BarcodeInfo barcodeInfo = findBarcodeInfoInCellFromStructured(cell, row, col);

                    if (barcodeInfo != null) {
                        // 找到了条码信息，生成独立的条码组件
                        if ("qrcode".equals(barcodeInfo.type)) {
                            LogKit.info("✅ 识别为二维码: " + barcodeInfo.content);
                            response.addQrCodeElement(cellMinX, cellMinY, cellWidth, cellHeight,
                                                    barcodeInfo.content != null ? barcodeInfo.content : "");
                            qrcodeCount++;
                        } else if ("barcode".equals(barcodeInfo.type)) {
                            LogKit.info("✅ 识别为条形码: " + barcodeInfo.content);
                            response.addBarcodeElement(cellMinX, cellMinY, cellWidth, cellHeight,
                                                     barcodeInfo.content != null ? barcodeInfo.content : "", "CODE_128");
                            barcodeCount++;
                        }
                    } else {
                        // 没有找到明确的条码信息，但检测到图片内容，尝试基于图片URL识别
                        String imageUrl = extractImageUrlFromCellText(cellText);
                        if (!StrKit.isBlank(imageUrl)) {
                            LogKit.info("🔗 从单元格提取到图片URL，尝试条码识别: " + imageUrl);

                            // 尝试识别为二维码
                            String qrContent = decodeFromImageUrl(imageUrl, "qrcode");
                            if (!StrKit.isBlank(qrContent)) {
                                LogKit.info("✅ URL识别为二维码: " + qrContent);
                                response.addQrCodeElement(cellMinX, cellMinY, cellWidth, cellHeight, qrContent);
                                qrcodeCount++;
                            } else {
                                // 尝试识别为条形码
                                String barcodeContent = decodeFromImageUrl(imageUrl, "barcode");
                                if (!StrKit.isBlank(barcodeContent)) {
                                    LogKit.info("✅ URL识别为条形码: " + barcodeContent);
                                    response.addBarcodeElement(cellMinX, cellMinY, cellWidth, cellHeight, barcodeContent, "CODE_128");
                                    barcodeCount++;
                                } else {
                                    LogKit.warn("⚠️ 无法识别图片URL中的条码内容，将添加空内容的二维码元素供ZXing补充");
                                    response.addQrCodeElement(cellMinX, cellMinY, cellWidth, cellHeight, "");
                                    qrcodeCount++;
                                }
                            }
                        } else {
                            LogKit.warn("⚠️ 检测到图片内容但未找到URL，将添加空内容的二维码元素供ZXing补充");
                            response.addQrCodeElement(cellMinX, cellMinY, cellWidth, cellHeight, "");
                            qrcodeCount++;
                        }
                    }
                } else {
                    LogKit.debug("单元格内容无图片标识，跳过: " + cellText);
                }

            } catch (Exception e) {
                LogKit.error("检测单元格条码时出错: " + e.getMessage(), e);
            }
        }

        LogKit.info("🎯 表格单元格条码检测完成: 生成了 " + qrcodeCount + " 个二维码组件, " + barcodeCount + " 个条形码组件");
    }

    /**
     * 【辅助数据类】条码信息
     */
    private static class BarcodeInfo {
        public String type;      // "qrcode" 或 "barcode"
        public String content;   // 条码内容
        public String imageUrl;  // 图片URL（如果有）

        public BarcodeInfo(String type, String content) {
            this.type = type;
            this.content = content;
        }

        public BarcodeInfo(String type, String content, String imageUrl) {
            this.type = type;
            this.content = content;
            this.imageUrl = imageUrl;
        }
    }

    /**
     * 【辅助方法】从structured数据中查找单元格对应的条码信息
     * 通过分析structured数组中的图像元素来确定单元格内是否包含条码
     *
     * @param cell detail单元格数据
     * @param row 单元格行号
     * @param col 单元格列号
     * @return 条码信息，如果未找到返回null
     */
    @SuppressWarnings("unchecked")
    private BarcodeInfo findBarcodeInfoInCellFromStructured(Map<String, Object> cell, Integer row, Integer col) {
        try {
            if (currentContentList == null || currentContentList.isEmpty()) {
                LogKit.debug("currentContentList为空，无法查找条码信息");
                return null;
            }

            // 获取单元格的位置边界
            List<Integer> cellPosition = (List<Integer>) cell.get("position");
            if (cellPosition == null || cellPosition.size() < 8) {
                return null;
            }

            int cellMinX = Math.min(Math.min(cellPosition.get(0), cellPosition.get(2)), Math.min(cellPosition.get(4), cellPosition.get(6)));
            int cellMinY = Math.min(Math.min(cellPosition.get(1), cellPosition.get(3)), Math.min(cellPosition.get(5), cellPosition.get(7)));
            int cellMaxX = Math.max(Math.max(cellPosition.get(0), cellPosition.get(2)), Math.max(cellPosition.get(4), cellPosition.get(6)));
            int cellMaxY = Math.max(Math.max(cellPosition.get(1), cellPosition.get(3)), Math.max(cellPosition.get(5), cellPosition.get(7)));

            LogKit.debug("查找单元格(" + row + "," + col + ")边界内的条码: [" + cellMinX + "," + cellMinY + "," + cellMaxX + "," + cellMaxY + "]");

            // 遍历content数组，查找位置在单元格范围内的image类型元素
            for (Map<String, Object> contentItem : currentContentList) {
                String type = (String) contentItem.get("type");
                String subType = (String) contentItem.get("sub_type");

                if ("image".equals(type) && ("qrcode".equals(subType) || "barcode".equals(subType))) {
                    List<Integer> imagePos = (List<Integer>) contentItem.get("pos");
                    if (imagePos != null && imagePos.size() >= 8) {
                        int imageMinX = Math.min(Math.min(imagePos.get(0), imagePos.get(2)), Math.min(imagePos.get(4), imagePos.get(6)));
                        int imageMinY = Math.min(Math.min(imagePos.get(1), imagePos.get(3)), Math.min(imagePos.get(5), imagePos.get(7)));
                        int imageMaxX = Math.max(Math.max(imagePos.get(0), imagePos.get(2)), Math.max(imagePos.get(4), imagePos.get(6)));
                        int imageMaxY = Math.max(Math.max(imagePos.get(1), imagePos.get(3)), Math.max(imagePos.get(5), imagePos.get(7)));

                        // 检测图像是否在单元格范围内（允许一些容差）
                        int tolerance = 20;
                        if (imageMinX >= cellMinX - tolerance && imageMinY >= cellMinY - tolerance &&
                            imageMaxX <= cellMaxX + tolerance && imageMaxY <= cellMaxY + tolerance) {

                            LogKit.info("🎯 找到单元格内的" + subType + "图像: [" + imageMinX + "," + imageMinY + "," + imageMaxX + "," + imageMaxY + "]");

                            // 尝试获取条码内容
                            String content = null;
                            String imageUrl = null;

                            if ("qrcode".equals(subType)) {
                                content = findQrCodeContent(currentContentList, contentItem);
                                imageUrl = findImageUrlFromContent(contentItem);
                            } else if ("barcode".equals(subType)) {
                                content = findBarcodeContent(currentContentList, contentItem);
                                imageUrl = findImageUrlFromContent(contentItem);
                            }

                            LogKit.info("条码内容: " + content + ", imageUrl: " + imageUrl);
                            return new BarcodeInfo(subType, content, imageUrl);
                        }
                    }
                }
            }

            LogKit.debug("未在单元格(" + row + "," + col + ")范围内找到条码图像");
            return null;

        } catch (Exception e) {
            LogKit.warn("查找单元格条码信息时出错: " + e.getMessage());
            return null;
        }
    }

    /**
     * 【辅助方法】从单元格文本中提取图片URL
     * 从HTML img标签或直接的URL中提取图片链接
     *
     * @param cellText 单元格文本内容
     * @return 图片URL，如果未找到返回null
     */
    private String extractImageUrlFromCellText(String cellText) {
        if (StrKit.isBlank(cellText)) {
            return null;
        }

        try {
            // 方法1：从HTML img标签中提取URL
            String imgUrl = extractUrlFromImgTag(cellText);
            if (!StrKit.isBlank(imgUrl)) {
                return imgUrl;
            }

            // 方法2：检查是否直接是URL格式
            if (cellText.startsWith("http")) {
                return cellText.trim();
            }

            // 方法3：使用正则表达式查找URL模式
            String urlPattern = "(https?://[^\\s]+\\.(jpg|jpeg|png|gif|bmp|webp))";
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(urlPattern, java.util.regex.Pattern.CASE_INSENSITIVE);
            java.util.regex.Matcher matcher = pattern.matcher(cellText);
            if (matcher.find()) {
                return matcher.group(1);
            }

        } catch (Exception e) {
            LogKit.warn("提取单元格图片URL时出错: " + e.getMessage());
        }

        return null;
    }

    /**
     * 【新增方法】检测structured表格单元格内的条码并生成独立的条码组件
     * 处理structured数组中的表格单元格条码识别
     *
     * @param cells structured表格的cells数组
     * @param tableX 表格的X坐标
     * @param tableY 表格的Y坐标
     * @param response OCR响应对象
     */
    @SuppressWarnings("unchecked")
    private void detectBarcodesInStructuredTableCells(List<Map<String, Object>> cells, int tableX, int tableY, OcrResponse response) {
        if (cells == null || cells.isEmpty()) {
            LogKit.info("structured表格没有cells数据，跳过单元格条码检测");
            return;
        }

        LogKit.info("🔍 开始检测structured表格单元格内的条码，单元格数量: " + cells.size());

        int barcodeCount = 0;
        int qrcodeCount = 0;

        for (int i = 0; i < cells.size(); i++) {
            try {
                Map<String, Object> cell = cells.get(i);

                // 获取单元格基本信息
                Integer row = (Integer) cell.get("row");
                Integer col = (Integer) cell.get("col");
                List<Integer> position = (List<Integer>) cell.get("pos");

                // 获取单元格文本内容（从content数组中获取）
                String cellText = getCellTextFromContent(cell);

                LogKit.debug("检测structured单元格[" + i + "] (" + row + "," + col + ") 文本: '" + cellText + "'");

                if (position == null || position.size() < 8) {
                    LogKit.debug("structured单元格位置信息无效，跳过");
                    continue;
                }

                // 计算单元格位置
                int cellMinX = Math.min(Math.min(position.get(0), position.get(2)), Math.min(position.get(4), position.get(6)));
                int cellMinY = Math.min(Math.min(position.get(1), position.get(3)), Math.min(position.get(5), position.get(7)));
                int cellMaxX = Math.max(Math.max(position.get(0), position.get(2)), Math.max(position.get(4), position.get(6)));
                int cellMaxY = Math.max(Math.max(position.get(1), position.get(3)), Math.max(position.get(5), position.get(7)));
                int cellWidth = cellMaxX - cellMinX;
                int cellHeight = cellMaxY - cellMinY;

                // 检测单元格内容中是否包含图片（可能是条码）
                boolean hasImageContent = detectImageContentInCell(cellText);

                if (hasImageContent) {
                    LogKit.info("📸 structured单元格(" + row + "," + col + ")检测到图片内容，尝试识别条码");

                    // 对于structured单元格，通过content字段查找对应的条码信息
                    BarcodeInfo barcodeInfo = findBarcodeInfoInStructuredCell(cell, row, col);

                    if (barcodeInfo != null) {
                        // 找到了条码信息，生成独立的条码组件
                        if ("qrcode".equals(barcodeInfo.type)) {
                            LogKit.info("✅ 识别为二维码: " + barcodeInfo.content);
                            response.addQrCodeElement(cellMinX, cellMinY, cellWidth, cellHeight,
                                                    barcodeInfo.content != null ? barcodeInfo.content : "");
                            qrcodeCount++;
                        } else if ("barcode".equals(barcodeInfo.type)) {
                            LogKit.info("✅ 识别为条形码: " + barcodeInfo.content);
                            response.addBarcodeElement(cellMinX, cellMinY, cellWidth, cellHeight,
                                                     barcodeInfo.content != null ? barcodeInfo.content : "", "CODE_128");
                            barcodeCount++;
                        }
                    } else {
                        // 没有找到明确的条码信息，但检测到图片内容，尝试基于图片URL识别
                        String imageUrl = extractImageUrlFromCellText(cellText);
                        if (!StrKit.isBlank(imageUrl)) {
                            LogKit.info("🔗 从structured单元格提取到图片URL，尝试条码识别: " + imageUrl);

                            // 尝试识别为二维码
                            String qrContent = decodeFromImageUrl(imageUrl, "qrcode");
                            if (!StrKit.isBlank(qrContent)) {
                                LogKit.info("✅ URL识别为二维码: " + qrContent);
                                response.addQrCodeElement(cellMinX, cellMinY, cellWidth, cellHeight, qrContent);
                                qrcodeCount++;
                            } else {
                                // 尝试识别为条形码
                                String barcodeContent = decodeFromImageUrl(imageUrl, "barcode");
                                if (!StrKit.isBlank(barcodeContent)) {
                                    LogKit.info("✅ URL识别为条形码: " + barcodeContent);
                                    response.addBarcodeElement(cellMinX, cellMinY, cellWidth, cellHeight, barcodeContent, "CODE_128");
                                    barcodeCount++;
                                } else {
                                    LogKit.warn("⚠️ 无法识别图片URL中的条码内容，将添加空内容的二维码元素供ZXing补充");
                                    response.addQrCodeElement(cellMinX, cellMinY, cellWidth, cellHeight, "");
                                    qrcodeCount++;
                                }
                            }
                        } else {
                            LogKit.warn("⚠️ 检测到图片内容但未找到URL，将添加空内容的二维码元素供ZXing补充");
                            response.addQrCodeElement(cellMinX, cellMinY, cellWidth, cellHeight, "");
                            qrcodeCount++;
                        }
                    }
                } else {
                    LogKit.debug("structured单元格内容无图片标识，跳过: " + cellText);
                }

            } catch (Exception e) {
                LogKit.error("检测structured单元格条码时出错: " + e.getMessage(), e);
            }
        }

        LogKit.info("🎯 structured表格单元格条码检测完成: 生成了 " + qrcodeCount + " 个二维码组件, " + barcodeCount + " 个条形码组件");
    }

    /**
     * 【辅助方法】从structured单元格中查找条码信息
     * structured单元格通过content字段关联到content数组中的具体元素
     *
     * @param structuredCell structured单元格数据
     * @param row 单元格行号
     * @param col 单元格列号
     * @return 条码信息，如果未找到返回null
     */
    @SuppressWarnings("unchecked")
    private BarcodeInfo findBarcodeInfoInStructuredCell(Map<String, Object> structuredCell, Integer row, Integer col) {
        try {
            if (currentContentList == null || currentContentList.isEmpty()) {
                LogKit.debug("currentContentList为空，无法查找条码信息");
                return null;
            }

            // 获取structured单元格通过content字段关联的content索引
            List<Integer> contentIndices = extractContentIndicesFromStructuredCell(structuredCell);
            if (contentIndices.isEmpty()) {
                LogKit.debug("structured单元格(" + row + "," + col + ")没有关联的content索引");
                return null;
            }

            LogKit.debug("structured单元格(" + row + "," + col + ")关联的content索引: " + contentIndices);

            // 检查关联的content元素是否包含条码
            for (Integer contentIndex : contentIndices) {
                if (contentIndex >= 0 && contentIndex < currentContentList.size()) {
                    Map<String, Object> contentItem = currentContentList.get(contentIndex);
                    String type = (String) contentItem.get("type");
                    String subType = (String) contentItem.get("sub_type");

                    if ("image".equals(type) && ("qrcode".equals(subType) || "barcode".equals(subType))) {
                        LogKit.info("🎯 在structured单元格(" + row + "," + col + ")的content[" + contentIndex + "]中找到" + subType);

                        // 获取条码内容
                        String content = null;
                        String imageUrl = null;

                        if ("qrcode".equals(subType)) {
                            content = findQrCodeContent(currentContentList, contentItem);
                            imageUrl = findImageUrlFromContent(contentItem);
                        } else if ("barcode".equals(subType)) {
                            content = findBarcodeContent(currentContentList, contentItem);
                            imageUrl = findImageUrlFromContent(contentItem);
                        }

                        LogKit.info("条码内容: " + content + ", imageUrl: " + imageUrl);
                        return new BarcodeInfo(subType, content, imageUrl);
                    }
                }
            }

            LogKit.debug("structured单元格(" + row + "," + col + ")的关联content中未找到条码");
            return null;

        } catch (Exception e) {
            LogKit.warn("查找structured单元格条码信息时出错: " + e.getMessage());
            return null;
        }
    }

    /**
     * 清理文本中的 markdown 格式符号
     * TextIn API 在 detail 字段中可能返回带 markdown 格式的文本，需要清理这些格式符号
     *
     * @param text 原始文本
     * @return 清理后的纯文本
     */
    private String cleanMarkdownFormatting(String text) {
        if (StrKit.isBlank(text)) {
            return text;
        }

        try {
            String cleanedText = text;

            // 清理粗体标记 **文本** -> 文本
            cleanedText = cleanedText.replaceAll("\\*\\*([^*]+)\\*\\*", "$1");

            // 清理斜体标记 *文本* -> 文本（但要避免与粗体标记冲突）
            cleanedText = cleanedText.replaceAll("(?<!\\*)\\*([^*]+)\\*(?!\\*)", "$1");

            // 清理下划线粗体标记 __文本__ -> 文本
            cleanedText = cleanedText.replaceAll("__([^_]+)__", "$1");

            // 清理下划线斜体标记 _文本_ -> 文本（但要避免与下划线粗体冲突）
            cleanedText = cleanedText.replaceAll("(?<!_)_([^_]+)_(?!_)", "$1");

            // 清理删除线标记 ~~文本~~ -> 文本
            cleanedText = cleanedText.replaceAll("~~([^~]+)~~", "$1");

            // 清理行内代码标记 `文本` -> 文本
            cleanedText = cleanedText.replaceAll("`([^`]+)`", "$1");

            // 如果文本发生了变化，记录日志
            if (!text.equals(cleanedText)) {
                LogKit.info("清理markdown格式符号: '" + text + "' -> '" + cleanedText + "'");
            }

            return cleanedText;

        } catch (Exception e) {
            LogKit.warn("清理markdown格式符号时出错: " + e.getMessage());
            return text; // 出错时返回原文本
        }
    }

    /**
     * 【新增方法】合并条形码与其对应的文本元素
     * 解决一维码和文本分成两个组件的问题
     *
     * @param response OCR响应对象
     */
    @SuppressWarnings("unchecked")
    private void mergeBarcodeWithText(OcrResponse response, TextInResponse textInResponse) {
        if (response.getElements() == null || response.getElements().isEmpty()) {
            LogKit.info("没有元素需要处理，跳过条形码文本合并");
            return;
        }

        LogKit.info("🔗 开始处理条形码与文本合并，元素总数: " + response.getElements().size());

        List<Map<String, Object>> elements = response.getElements();
        List<Integer> elementsToRemove = new ArrayList<>();
        int mergedCount = 0;

        // 遍历所有条形码元素
        LogKit.info("🔄 开始遍历" + elements.size() + "个元素寻找条形码");
        for (int i = 0; i < elements.size(); i++) {
            Map<String, Object> element = elements.get(i);
            String elementType = String.valueOf(element.get("elementType"));
            LogKit.debug("检查元素[" + i + "]: elementType=" + elementType);

            // 只处理条形码元素（elementType = "2"）
            if ("2".equals(elementType)) {
                LogKit.info("✅ 发现条形码元素[" + i + "]，elementType=" + elementType);
                String barcodeContent = (String) element.get("content");
                Integer barcodeX = (Integer) element.get("x");
                Integer barcodeY = (Integer) element.get("y");
                Integer barcodeWidth = (Integer) element.get("width");
                Integer barcodeHeight = (Integer) element.get("height");

                if (StrKit.isBlank(barcodeContent) || barcodeX == null || barcodeY == null ||
                    barcodeWidth == null || barcodeHeight == null) {
                    LogKit.info("❌ 条形码[" + i + "]信息不完整，跳过合并: content='" + barcodeContent + "', pos=[" + barcodeX + "," + barcodeY + "," + barcodeWidth + "," + barcodeHeight + "]");
                    continue;
                }

                LogKit.debug("处理条形码[" + i + "]: 内容='" + barcodeContent + "', 位置=[" + barcodeX + "," + barcodeY + "," + barcodeWidth + "," + barcodeHeight + "]");

                // 查找与条形码内容完全匹配且位置相近的文本元素
                TextPosition matchedText = findMatchingTextForBarcode(elements, barcodeContent,
                                                                    barcodeX, barcodeY, barcodeWidth, barcodeHeight, i, textInResponse);

                if (matchedText != null) {
                    // 更新条形码元素的showText属性
                    element.put("showText", matchedText.position);

                    // 添加文本尺寸字段
                    element.put("textWidth", matchedText.textWidth);
                    element.put("textHeight", matchedText.textHeight);

                    // 根据文本位置更新条形码总高度
                    if (matchedText.position == 2 || matchedText.position == 3) {
                        // 文本在上方或下方时，将文本高度加入条形码总高度
                        int originalHeight = barcodeHeight;
                        int newHeight = originalHeight + matchedText.textHeight;
                        element.put("height", newHeight);
                        LogKit.info("更新条形码[" + i + "]总高度: " + originalHeight + " -> " + newHeight + " (增加文本高度 " + matchedText.textHeight + ")");
                    }

                    // 只有当匹配来自elements时才标记删除
                    if (matchedText.elementIndex >= 0) {
                        // 标记文本元素待删除
                        elementsToRemove.add(matchedText.elementIndex);
                        LogKit.info("✅ 合并成功: 条形码[" + i + "] + 文本[" + matchedText.elementIndex + "], showText=" +
                                   (matchedText.position == 2 ? "上方" : "下方") + ", 文本尺寸=[" + matchedText.textWidth + "x" + matchedText.textHeight + "]");
                    } else {
                        // 来自pages-content的匹配，需要从structured中移除该content索引
                        LogKit.info("✅ 合并成功: 条形码[" + i + "] + pages-content文本, showText=" +
                                   (matchedText.position == 2 ? "上方" : "下方") + ", 文本尺寸=[" + matchedText.textWidth + "x" + matchedText.textHeight + "]");

                        // 处理structured数据的更新
                        if (matchedText.contentIndex >= 0 && matchedText.structuredIndex >= 0) {
                            removeContentFromStructured(elements, textInResponse, matchedText.contentIndex,
                                                      matchedText.structuredIndex, elementsToRemove);
                        }
                    }
                    mergedCount++;
                } else {
                    // 没有找到匹配的文本，保持showText为1
                    element.put("showText", 1);
                    // 设置默认的文本尺寸字段
                    element.put("textWidth", 0);
                    element.put("textHeight", 0);
                    LogKit.debug("条形码[" + i + "]未找到匹配的文本元素");
                }
            }
        }

        // 按降序删除元素（避免索引变化问题）
        elementsToRemove.sort((a, b) -> b.compareTo(a));
        for (Integer indexToRemove : elementsToRemove) {
            elements.remove(indexToRemove.intValue());
            LogKit.debug("已删除文本元素[" + indexToRemove + "]");
        }

        LogKit.info("🎯 条形码文本合并完成: 合并了 " + mergedCount + " 个条形码，删除了 " + elementsToRemove.size() + " 个文本元素");
        LogKit.info("处理后元素总数: " + response.getElements().size());
    }

    /**
     * 【辅助方法】基于structured数据精确移除条形码内容
     *
     * @param elements 所有元素列表
     * @param textInResponse TextIn原始响应数据
     * @param contentIndex 要移除的content索引
     * @param structuredIndex 包含该content的structured索引
     * @param elementsToRemove 待删除元素索引列表
     */
    @SuppressWarnings("unchecked")
    private void removeContentFromStructured(List<Map<String, Object>> elements,
                                           TextInResponse textInResponse,
                                           int contentIndex,
                                           int structuredIndex,
                                           List<Integer> elementsToRemove) {
        LogKit.debug("开始从structured[" + structuredIndex + "]中移除content[" + contentIndex + "]");

        try {
            List<Map<String, Object>> pages = textInResponse.getPages();
            if (pages == null || pages.isEmpty()) {
                LogKit.warn("textInResponse中没有pages数据");
                return;
            }

            Map<String, Object> firstPage = pages.get(0);
            List<Map<String, Object>> contentList = (List<Map<String, Object>>) firstPage.get("content");
            List<Map<String, Object>> structuredList = (List<Map<String, Object>>) firstPage.get("structured");

            if (contentList == null || structuredList == null ||
                structuredIndex >= structuredList.size()) {
                LogKit.warn("数据结构不完整或索引越界");
                return;
            }

            Map<String, Object> structuredItem = structuredList.get(structuredIndex);
            List<Integer> contentIndices = (List<Integer>) structuredItem.get("content");

            if (contentIndices == null || !contentIndices.contains(contentIndex)) {
                LogKit.warn("structured[" + structuredIndex + "]不包含content[" + contentIndex + "]");
                return;
            }

            // 从structured的content数组中移除该索引
            contentIndices.remove(Integer.valueOf(contentIndex));
            LogKit.info("从structured[" + structuredIndex + "]中移除了content[" + contentIndex + "]，剩余: " + contentIndices);

            // 重建文本内容
            String rebuiltText = rebuildTextFromContentIndices(contentList, contentIndices);
            LogKit.info("重建文本内容: '" + rebuiltText + "'");

            // 更新elements中对应的文本元素
            updateElementsWithRebuiltText(elements, structuredItem, rebuiltText, elementsToRemove, contentList, contentIndices);

        } catch (Exception e) {
            LogKit.error("移除structured内容时发生错误: " + e.getMessage(), e);
        }
    }

    /**
     * 根据content索引数组重建文本内容
     */
    @SuppressWarnings("unchecked")
    private String rebuildTextFromContentIndices(List<Map<String, Object>> contentList,
                                               List<Integer> contentIndices) {
        StringBuilder rebuiltText = new StringBuilder();

        for (Integer index : contentIndices) {
            if (index >= 0 && index < contentList.size()) {
                Map<String, Object> contentItem = contentList.get(index);
                String text = (String) contentItem.get("text");
                if (text != null && !text.trim().isEmpty()) {
                    if (rebuiltText.length() > 0) {
                        rebuiltText.append(" ");
                    }
                    rebuiltText.append(text);
                }
            }
        }

        return rebuiltText.toString();
    }

    /**
     * 更新elements中对应的文本元素内容，并重新计算宽度和charWidth
     */
    @SuppressWarnings("unchecked")
    private void updateElementsWithRebuiltText(List<Map<String, Object>> elements,
                                             Map<String, Object> structuredItem,
                                             String rebuiltText,
                                             List<Integer> elementsToRemove,
                                             List<Map<String, Object>> contentList,
                                             List<Integer> remainingContentIndices) {
        // 获取structured元素的位置信息，用于匹配elements中的文本元素
        List<Integer> structuredPos = (List<Integer>) structuredItem.get("pos");
        if (structuredPos == null || structuredPos.size() < 8) {
            LogKit.warn("structured元素位置信息不完整");
            return;
        }

        int structuredX = Math.min(Math.min(structuredPos.get(0), structuredPos.get(2)),
                                  Math.min(structuredPos.get(4), structuredPos.get(6)));
        int structuredY = Math.min(Math.min(structuredPos.get(1), structuredPos.get(3)),
                                  Math.min(structuredPos.get(5), structuredPos.get(7)));

        // 根据剩余content项的position信息计算准确的宽度和高度
        int[] newDimensions = calculateDimensionsFromContentPositions(contentList, remainingContentIndices);
        int newWidth = newDimensions[0];
        int newHeight = newDimensions[1];

        // 在elements中查找对应的文本元素
        for (int i = 0; i < elements.size(); i++) {
            Map<String, Object> element = elements.get(i);
            String elementType = String.valueOf(element.get("elementType"));

            // 只处理文本元素
            if (!"1".equals(elementType)) {
                continue;
            }

            Integer elementX = (Integer) element.get("x");
            Integer elementY = (Integer) element.get("y");

            if (elementX == null || elementY == null) {
                continue;
            }

            // 位置匹配（允许一定的容差）
            int tolerance = 10;
            if (Math.abs(elementX - structuredX) <= tolerance &&
                Math.abs(elementY - structuredY) <= tolerance) {

                String oldContent = (String) element.get("content");
                LogKit.info("找到匹配的元素[" + i + "], 更新内容: '" + oldContent + "' -> '" + rebuiltText + "'");

                if (rebuiltText.trim().isEmpty()) {
                    // 如果重建后内容为空，标记删除
                    elementsToRemove.add(i);
                    LogKit.info("元素[" + i + "]内容为空，标记删除");
                } else {
                    // 更新元素内容
                    element.put("content", rebuiltText);

                    // 使用基于content position计算的准确宽度和高度
                    element.put("width", newWidth);
                    element.put("height", newHeight);

                    // 重新计算charWidth
                    double newCharWidth = rebuiltText.length() > 0 ? (double) newWidth / rebuiltText.length() : 0.0;
                    element.put("charWidth", Math.round(newCharWidth * 10.0) / 10.0);

                    LogKit.info("元素[" + i + "]内容已更新，新宽度: " + newWidth + ", 新高度: " + newHeight + ", 新charWidth: " + newCharWidth);
                }

                break; // 只处理第一个匹配的元素
            }
        }
    }

    /**
     * 根据剩余content项的position信息计算准确的宽度和高度
     *
     * @param contentList 完整的content列表
     * @param remainingContentIndices 剩余的content索引列表
     * @return int[] {width, height}
     */
    @SuppressWarnings("unchecked")
    private int[] calculateDimensionsFromContentPositions(List<Map<String, Object>> contentList,
                                                         List<Integer> remainingContentIndices) {
        if (contentList == null || remainingContentIndices == null || remainingContentIndices.isEmpty()) {
            LogKit.warn("无法计算dimensions：contentList或remainingContentIndices为空");
            return new int[]{0, 0};
        }

        int minX = Integer.MAX_VALUE, minY = Integer.MAX_VALUE;
        int maxX = Integer.MIN_VALUE, maxY = Integer.MIN_VALUE;

        LogKit.debug("开始计算dimensions，剩余content索引: " + remainingContentIndices);

        for (Integer contentIndex : remainingContentIndices) {
            if (contentIndex >= 0 && contentIndex < contentList.size()) {
                Map<String, Object> contentItem = contentList.get(contentIndex);
                List<Integer> pos = (List<Integer>) contentItem.get("pos");
                String text = (String) contentItem.get("text");

                if (pos != null && pos.size() >= 8) {
                    // 计算该content的边界
                    int itemMinX = Math.min(Math.min(pos.get(0), pos.get(2)), Math.min(pos.get(4), pos.get(6)));
                    int itemMinY = Math.min(Math.min(pos.get(1), pos.get(3)), Math.min(pos.get(5), pos.get(7)));
                    int itemMaxX = Math.max(Math.max(pos.get(0), pos.get(2)), Math.max(pos.get(4), pos.get(6)));
                    int itemMaxY = Math.max(Math.max(pos.get(1), pos.get(3)), Math.max(pos.get(5), pos.get(7)));

                    minX = Math.min(minX, itemMinX);
                    minY = Math.min(minY, itemMinY);
                    maxX = Math.max(maxX, itemMaxX);
                    maxY = Math.max(maxY, itemMaxY);

                    LogKit.debug("content[" + contentIndex + "] '" + text + "' 边界: " +
                               "x=" + itemMinX + "-" + itemMaxX + " (width=" + (itemMaxX - itemMinX) + "), " +
                               "y=" + itemMinY + "-" + itemMaxY + " (height=" + (itemMaxY - itemMinY) + ")");
                }
            }
        }

        if (minX == Integer.MAX_VALUE || minY == Integer.MAX_VALUE ||
            maxX == Integer.MIN_VALUE || maxY == Integer.MIN_VALUE) {
            LogKit.warn("无法计算有效边界");
            return new int[]{0, 0};
        }

        int width = maxX - minX;
        int height = maxY - minY;

        LogKit.info("根据" + remainingContentIndices.size() + "个content项计算得到dimensions: " +
                   width + "x" + height + " (边界: x=" + minX + "-" + maxX + ", y=" + minY + "-" + maxY + ")");

        return new int[]{width, height};
    }

    /**
     * 【辅助数据类】文本位置信息
     */
    private static class TextPosition {
        public int elementIndex;        // 文本元素在列表中的索引
        public int position;            // 文本位置：2-上方，3-下方
        public int contentIndex;        // 匹配的pages-content索引（用于structured移除）
        public int structuredIndex;     // 包含该content的structured索引
        public int textWidth;           // 文本的宽度
        public int textHeight;          // 文本的高度

        public TextPosition(int elementIndex, int position) {
            this(elementIndex, position, -1, -1, 0, 0);
        }

        public TextPosition(int elementIndex, int position, int contentIndex, int structuredIndex) {
            this(elementIndex, position, contentIndex, structuredIndex, 0, 0);
        }

        public TextPosition(int elementIndex, int position, int contentIndex, int structuredIndex, int textWidth, int textHeight) {
            this.elementIndex = elementIndex;
            this.position = position;
            this.contentIndex = contentIndex;
            this.structuredIndex = structuredIndex;
            this.textWidth = textWidth;
            this.textHeight = textHeight;
        }
    }

    /**
     * 【辅助方法】查找与条形码匹配的文本元素
     *
     * @param elements 所有元素列表
     * @param barcodeContent 条形码内容
     * @param barcodeX 条形码X坐标
     * @param barcodeY 条形码Y坐标
     * @param barcodeWidth 条形码宽度
     * @param barcodeHeight 条形码高度
     * @param barcodeIndex 条形码元素索引（用于排除自身）
     * @return 匹配的文本位置信息，如果未找到返回null
     */
    @SuppressWarnings("unchecked")
    private TextPosition findMatchingTextForBarcode(List<Map<String, Object>> elements,
                                                   String barcodeContent,
                                                   int barcodeX, int barcodeY,
                                                   int barcodeWidth, int barcodeHeight,
                                                   int barcodeIndex,
                                                   TextInResponse textInResponse) {

        LogKit.info("🔍 开始查找条形码'" + barcodeContent + "'的匹配文本，textInResponse=" + (textInResponse != null ? "不为null" : "null"));

        // 计算条形码的边界
        int barcodeTop = barcodeY;
        int barcodeBottom = barcodeY + barcodeHeight;
        int barcodeLeft = barcodeX;
        int barcodeRight = barcodeX + barcodeWidth;
        int barcodeCenterX = barcodeX + barcodeWidth / 2;

        // 设置搜索范围和容差
        int verticalTolerance = 100;  // 垂直距离容差（像素）
        int horizontalTolerance = 200;  // 水平距离容差（像素）

        TextPosition bestMatch = null;
        int minDistance = Integer.MAX_VALUE;

        LogKit.debug("条形码边界: top=" + barcodeTop + ", bottom=" + barcodeBottom +
                    ", left=" + barcodeLeft + ", right=" + barcodeRight + ", centerX=" + barcodeCenterX);

        // 1. 首先检查已处理的文本元素（原有逻辑）
        for (int i = 0; i < elements.size(); i++) {
            if (i == barcodeIndex) continue;  // 跳过条形码自身

            Map<String, Object> element = elements.get(i);
            String elementType = String.valueOf(element.get("elementType"));

            // 只检查文本元素（elementType = "1"）
            if (!"1".equals(elementType)) continue;

            String textContent = (String) element.get("content");
            Integer textX = (Integer) element.get("x");
            Integer textY = (Integer) element.get("y");
            Integer textWidth = (Integer) element.get("width");
            Integer textHeight = (Integer) element.get("height");

            if (textX == null || textY == null || textWidth == null || textHeight == null) {
                LogKit.debug("文本元素[" + i + "]位置信息不完整，跳过");
                continue;
            }

            // 检查内容是否完全匹配
            if (!barcodeContent.equals(textContent)) {
                LogKit.debug("文本元素[" + i + "]内容不匹配: '" + textContent + "' != '" + barcodeContent + "'");
                continue;
            }

            LogKit.debug("发现内容匹配的文本元素[" + i + "]: '" + textContent + "', 位置=[" + textX + "," + textY + "," + textWidth + "," + textHeight + "]");

            // 计算文本的边界和中心
            int textTop = textY;
            int textBottom = textY + textHeight;
            int textLeft = textX;
            int textRight = textX + textWidth;
            int textCenterX = textX + textWidth / 2;

            // 检查水平对齐（文本中心X与条形码中心X的距离）
            int horizontalDistance = Math.abs(textCenterX - barcodeCenterX);
            if (horizontalDistance > horizontalTolerance) {
                LogKit.debug("文本元素[" + i + "]水平距离过远: " + horizontalDistance + " > " + horizontalTolerance);
                continue;
            }

            // 判断文本位置：上方还是下方
            int position = 0;
            int verticalDistance = 0;

            if (textBottom <= barcodeTop + verticalTolerance) {
                // 文本在条形码上方
                position = 2;
                verticalDistance = barcodeTop - textBottom;
                LogKit.debug("文本元素[" + i + "]在条形码上方，垂直距离: " + verticalDistance);
            } else if (textTop >= barcodeBottom - verticalTolerance) {
                // 文本在条形码下方
                position = 3;
                verticalDistance = textTop - barcodeBottom;
                LogKit.debug("文本元素[" + i + "]在条形码下方，垂直距离: " + verticalDistance);
            } else {
                LogKit.debug("文本元素[" + i + "]与条形码重叠，不符合上下关系");
                continue;
            }

            // 检查垂直距离是否在合理范围内
            if (verticalDistance > verticalTolerance) {
                LogKit.debug("文本元素[" + i + "]垂直距离过远: " + verticalDistance + " > " + verticalTolerance);
                continue;
            }

            // 计算总体距离（水平距离 + 垂直距离）
            int totalDistance = horizontalDistance + verticalDistance;

            // 选择距离最近的匹配文本
            if (totalDistance < minDistance) {
                minDistance = totalDistance;
                bestMatch = new TextPosition(i, position, -1, -1, textWidth, textHeight);
                LogKit.debug("更新最佳匹配: 文本[" + i + "], 位置=" + (position == 2 ? "上方" : "下方") + ", 总距离=" + totalDistance + ", 文本尺寸=[" + textWidth + "x" + textHeight + "]");
            }
        }

        // 2. 如果没有找到匹配，检查TextIn原始数据中的pages-content数组
        LogKit.debug("检查是否需要在pages-content中查找: bestMatch=" + (bestMatch != null ? "已找到" : "null") + ", textInResponse=" + (textInResponse != null ? "不为null" : "null"));
        if (bestMatch == null && textInResponse != null) {
            List<Map<String, Object>> pages = textInResponse.getPages();
            LogKit.debug("pages数量: " + (pages != null ? pages.size() : "null"));
            if (pages != null && !pages.isEmpty()) {
                Map<String, Object> firstPage = pages.get(0);
                List<Map<String, Object>> contentList = (List<Map<String, Object>>) firstPage.get("content");
                List<Map<String, Object>> structuredList = (List<Map<String, Object>>) firstPage.get("structured");

                LogKit.debug("content数量: " + (contentList != null ? contentList.size() : "null") + ", structured数量: " + (structuredList != null ? structuredList.size() : "null"));
                if (contentList != null && structuredList != null) {
                    LogKit.info("未在elements中找到匹配，开始检查pages-content数组，查找内容: '" + barcodeContent + "'");

                    for (int contentIdx = 0; contentIdx < contentList.size(); contentIdx++) {
                        Map<String, Object> contentItem = contentList.get(contentIdx);
                        String contentText = (String) contentItem.get("text");

                        LogKit.debug("检查content[" + contentIdx + "]: '" + contentText + "' 是否匹配 '" + barcodeContent + "'");

                        // 检查内容是否完全匹配
                        if (!barcodeContent.equals(contentText)) {
                            LogKit.debug("content[" + contentIdx + "]不匹配: '" + contentText + "' != '" + barcodeContent + "'");
                            continue;
                        }

                        // 查找包含该content索引的structured元素
                        int structuredIdx = findStructuredContainingContent(structuredList, contentIdx);
                        if (structuredIdx == -1) {
                            LogKit.debug("未找到包含content[" + contentIdx + "]的structured元素");
                            continue;
                        }

                        LogKit.debug("找到匹配: content[" + contentIdx + "] 被 structured[" + structuredIdx + "] 包含");

                        // 获取位置信息
                        List<Integer> pos = (List<Integer>) contentItem.get("pos");
                        if (pos == null || pos.size() < 8) {
                            continue;
                        }

                        // 计算文本区域边界（TextIn使用8点坐标，取最小最大值）
                        int textLeft = Math.min(Math.min(toInt(pos.get(0)), toInt(pos.get(2))), Math.min(toInt(pos.get(4)), toInt(pos.get(6))));
                        int textTop = Math.min(Math.min(toInt(pos.get(1)), toInt(pos.get(3))), Math.min(toInt(pos.get(5)), toInt(pos.get(7))));
                        int textRight = Math.max(Math.max(toInt(pos.get(0)), toInt(pos.get(2))), Math.max(toInt(pos.get(4)), toInt(pos.get(6))));
                        int textBottom = Math.max(Math.max(toInt(pos.get(1)), toInt(pos.get(3))), Math.max(toInt(pos.get(5)), toInt(pos.get(7))));

                        int textCenterX = (textLeft + textRight) / 2;

                        LogKit.debug("pages-content[" + contentIdx + "]匹配内容: '" + contentText + "', 位置=[" + textLeft + "," + textTop + "," + textRight + "," + textBottom + "]");

                        // 检查水平对齐
                        int horizontalDistance = Math.abs(textCenterX - barcodeCenterX);
                        if (horizontalDistance > horizontalTolerance) {
                            LogKit.debug("pages-content[" + contentIdx + "]水平距离过远: " + horizontalDistance + " > " + horizontalTolerance);
                            continue;
                        }

                        // 判断文本位置：上方还是下方
                        int position = 0;
                        int verticalDistance = 0;

                        if (textBottom <= barcodeTop + verticalTolerance) {
                            // 文本在条形码上方
                            position = 2;
                            verticalDistance = barcodeTop - textBottom;
                            LogKit.debug("pages-content[" + contentIdx + "]在条形码上方，垂直距离: " + verticalDistance);
                        } else if (textTop >= barcodeBottom - verticalTolerance) {
                            // 文本在条形码下方
                            position = 3;
                            verticalDistance = textTop - barcodeBottom;
                            LogKit.debug("pages-content[" + contentIdx + "]在条形码下方，垂直距离: " + verticalDistance);
                        } else {
                            LogKit.debug("pages-content[" + contentIdx + "]与条形码重叠，不符合上下关系");
                            continue;
                        }

                        // 检查垂直距离是否在合理范围内
                        if (verticalDistance > verticalTolerance) {
                            LogKit.debug("pages-content[" + contentIdx + "]垂直距离过远: " + verticalDistance + " > " + verticalTolerance);
                            continue;
                        }

                        // 计算总体距离
                        int totalDistance = horizontalDistance + verticalDistance;

                        // 选择距离最近的匹配文本
                        if (totalDistance < minDistance) {
                            minDistance = totalDistance;
                            // 计算文本宽高
                            int textWidth = textRight - textLeft;
                            int textHeight = textBottom - textTop;
                            // 记录content索引和structured索引，用于后续的精确移除
                            bestMatch = new TextPosition(-1, position, contentIdx, structuredIdx, textWidth, textHeight);
                            LogKit.debug("更新最佳匹配(pages-content): content[" + contentIdx + "], structured[" + structuredIdx + "], 位置=" + (position == 2 ? "上方" : "下方") + ", 总距离=" + totalDistance + ", 文本尺寸=[" + textWidth + "x" + textHeight + "]");
                        }
                    }
                }
            }
        }

        if (bestMatch != null) {
            if (bestMatch.elementIndex >= 0) {
                LogKit.info("找到最佳匹配文本[" + bestMatch.elementIndex + "]，位置=" +
                           (bestMatch.position == 2 ? "上方" : "下方") + "，距离=" + minDistance);
            } else {
                LogKit.info("找到最佳匹配(pages-content)，位置=" +
                           (bestMatch.position == 2 ? "上方" : "下方") + "，距离=" + minDistance);
            }
        } else {
            LogKit.debug("未找到匹配的文本元素");
        }

        return bestMatch;
    }

    /**
     * 查找包含指定content索引的structured元素
     *
     * @param structuredList structured数组
     * @param contentIndex 要查找的content索引
     * @return structured元素的索引，如果未找到返回-1
     */
    @SuppressWarnings("unchecked")
    private int findStructuredContainingContent(List<Map<String, Object>> structuredList, int contentIndex) {
        for (int i = 0; i < structuredList.size(); i++) {
            Map<String, Object> structuredItem = structuredList.get(i);
            List<Integer> contentIndices = (List<Integer>) structuredItem.get("content");

            if (contentIndices != null && contentIndices.contains(contentIndex)) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 安全地将Object转换为int（处理Double/Integer类型）
     */
    private int toInt(Object obj) {
        if (obj instanceof Number) {
            return ((Number) obj).intValue();
        }
        return 0;
    }
}
