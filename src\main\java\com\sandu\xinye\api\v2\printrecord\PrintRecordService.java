package com.sandu.xinye.api.v2.printrecord;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.PrintRecord;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 打印记录服务类
 *
 * <AUTHOR> Team
 */
public class PrintRecordService {
    public static final PrintRecordService me = new PrintRecordService();

    /**
     * 获取模板打印记录列表
     *
     * @param userId 用户ID
     * @param pageNumber 页码
     * @param pageSize 页大小
     * @return 打印记录列表
     */
    public RetKit getTemplatePrintRecordList(Integer userId, int pageNumber, int pageSize) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("userId", userId);
            params.put("printType", Constant.PRINT_RECORD_TYPE_TEMPLATE);

            SqlPara sqlPara = Db.getSqlPara("app.printRecord.template.list", params);

            Page<PrintRecord> page = PrintRecord.dao.paginate(pageNumber, pageSize, sqlPara);

            // 转换数据格式
            List<Map<String, Object>> list = convertPrintRecordList(page.getList());

            Map<String, Object> result = new HashMap<>();
            result.put("list", list);
            result.put("totalRow", page.getTotalRow());
            result.put("pageNumber", page.getPageNumber());
            result.put("pageSize", page.getPageSize());
            result.put("totalPage", page.getTotalPage());

            return RetKit.ok("data", result);
        } catch (Exception e) {
            LogKit.error("获取模板打印记录列表失败", e);
            return RetKit.fail("获取打印记录失败");
        }
    }

    /**
     * 搜索模板打印记录
     *
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @param width 打印宽度
     * @param height 打印高度
     * @param pageNumber 页码
     * @param pageSize 页大小
     * @return 搜索结果
     */
    public RetKit searchTemplatePrintRecord(Integer userId, String keyword, Integer width, Integer height,
                                          int pageNumber, int pageSize) {
        try {
            // 约束查询参数，避免滥用
            pageNumber = Math.max(1, pageNumber);
            pageSize = Math.min(Math.max(1, pageSize), 100);

            // 限制 keyword 长度，防止 DoS 攻击
            if (keyword != null && keyword.trim().length() > 100) {
                return RetKit.fail("搜索关键词过长");
            }
            if (keyword != null) {
                keyword = keyword.trim();
            }

            Map<String, Object> params = new HashMap<>();
            params.put("userId", userId);
            params.put("printType", Constant.PRINT_RECORD_TYPE_TEMPLATE);

            if (StrKit.notBlank(keyword)) {
                String safe = escapeLikePattern(keyword);
                params.put("keyword", "%" + safe + "%");
            }
            if (width != null) {
                params.put("width", width);
            }
            if (height != null) {
                params.put("height", height);
            }

            SqlPara sqlPara = Db.getSqlPara("app.printRecord.template.search", params);
            Page<PrintRecord> page = PrintRecord.dao.paginate(pageNumber, pageSize, sqlPara);

            // 转换数据格式
            List<Map<String, Object>> list = convertPrintRecordList(page.getList());

            Map<String, Object> result = new HashMap<>();
            result.put("list", list);
            result.put("totalRow", page.getTotalRow());
            result.put("pageNumber", page.getPageNumber());
            result.put("pageSize", page.getPageSize());
            result.put("totalPage", page.getTotalPage());

            return RetKit.ok("data", result);

        } catch (Exception e) {
            LogKit.error("搜索模板打印记录失败", e);
            return RetKit.fail("搜索打印记录失败");
        }
    }

    /**
     * 删除打印记录（软删除）
     *
     * @param userId 用户ID
     * @param recordId 记录ID
     * @return 删除结果
     */
    public RetKit deletePrintRecord(Integer userId, Long recordId) {
        try {
            PrintRecord record = PrintRecord.dao.findById(recordId);
            if (record == null) {
                return RetKit.fail("打印记录不存在");
            }

            // 检查权限
            if (!userId.equals(record.getUserId())) {
                return RetKit.fail("无权限删除该记录");
            }

            // 软删除
            record.setDeleteTime(new Date());
            boolean success = record.update();

            return success ? RetKit.ok("删除成功") : RetKit.fail("删除失败");
        } catch (Exception e) {
            LogKit.error("删除打印记录失败", e);
            return RetKit.fail("删除失败");
        }
    }

    /**
     * 获取打印记录详情
     *
     * @param userId 用户ID
     * @param recordId 记录ID
     * @return 记录详情
     */
    public RetKit getPrintRecordDetail(Integer userId, Long recordId) {
        try {
            PrintRecord record = PrintRecord.dao.findById(recordId);
            if (record == null || record.isDeleted()) {
                return RetKit.fail("打印记录不存在");
            }

            // 检查权限
            if (!userId.equals(record.getUserId())) {
                return RetKit.fail("无权限访问该记录");
            }

            Map<String, Object> data = convertPrintRecord(record);
            return RetKit.ok("data", data);
        } catch (Exception e) {
            LogKit.error("获取打印记录详情失败", e);
            return RetKit.fail("获取记录详情失败");
        }
    }

    /**
     * 保存模板打印记录
     *
     * @param userId 用户ID
     * @param templateId 模板ID
     * @param templateName 模板名称
     * @param templateCover 模板封面
     * @param printWidth 打印宽度
     * @param printHeight 打印高度
     * @param printCopies 打印份数
     * @param printPlatform 打印端
     * @param templateData 模板数据
     * @return 保存结果，包含 recorded 字段表示是否实际记录
     */
    public RetKit saveTemplatePrintRecord(Integer userId, Integer templateId, String templateName,
                                        String templateCover, Integer printWidth, Integer printHeight,
                                        Integer printCopies, Integer printPlatform, String templateData) {
        try {
            Date now = new Date();

            // 检查用户是否开启打印记录
            boolean recordEnabled = UserPrintSettingService.me.isRecordEnabled(userId);

            Long recordId = null;
            boolean recordSaved = false;

            // 仅当开关开启时才保存打印记录
            PrintRecord record = null;
            if (recordEnabled) {
                record = new PrintRecord();
                record.setUserId(userId)
                      .setPrintType(Constant.PRINT_RECORD_TYPE_TEMPLATE)
                      .setSourceId(templateId)
                      .setSourceName(templateName)
                      .setSourceCover(templateCover)
                      .setPrintWidth(printWidth)
                      .setPrintHeight(printHeight)
                      .setPrintCopies(printCopies)
                      .setPrintPlatform(printPlatform)
                      .setPrintTime(now)
                      .setSourceData(templateData)
                      .setPrintStatus(Constant.PRINT_STATUS_SUCCESS);

                recordSaved = record.save();
                if (recordSaved) {
                    recordId = record.getId();
                }
            } else {
                // 记录被抑制的日志
                LogKit.info("用户 " + userId + " 的打印记录已被抑制（开关已关闭）- 模板: " + templateName);
            }

            // 无论开关状态如何，都要更新用户日打印统计
            try {

                try {
                    Db.update("INSERT INTO user_print_day (user_id, print_date, print_count, first_print_time, last_print_time) " +
                              "VALUES (?, DATE(?), 1, ?, ?) " +
                              "ON DUPLICATE KEY UPDATE print_count = print_count + 1, last_print_time = VALUES(last_print_time)",
                              userId, now, now, now);
                } catch (Exception ex) {
                    LogKit.error("更新 user_print_day 失败 - userId:" + userId + ", err:" + ex.getMessage(), ex);
                }

                // 触发“激活事件”判断与上报（actionType=2），达标仅上报一次
                try {
                    com.sandu.xinye.api.v2.promotion.PromotionService.me.tryReportActivationOnPrint(userId, now, recordId);
                } catch (Exception ex) {
                    LogKit.error("触发推广上报异常 - userId:" + userId + ", recordId:" + recordId + ", err:" + ex.getMessage(), ex);
                }
            }
            // 返回结果，包含 recorded 字段
            RetKit result = RetKit.ok("操作成功");
            result.put("recorded", recordEnabled && recordSaved);
            return result;
        } catch (Exception e) {
            LogKit.error("保存模板打印记录失败", e);
            return RetKit.fail("保存失败");
        }
    }



    /**
     * 转换打印记录列表数据格式
     *
     * @param records 打印记录列表
     * @return 转换后的数据列表
     */
    private List<Map<String, Object>> convertPrintRecordList(List<PrintRecord> records) {
        return records.stream().map(this::convertPrintRecord).collect(Collectors.toList());
    }

    /**
     * 转换单个打印记录数据格式
     *
     * @param record 打印记录
     * @return 转换后的数据
     */
    private static String escapeLikePattern(String s) {
        if (s == null || s.isEmpty()) return s;
        return s.replace("\\", "\\\\").replace("%", "\\%").replace("_", "\\_");
    }

    private Map<String, Object> convertPrintRecord(PrintRecord record) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", record.getId());
        data.put("templateId", record.getSourceId());
        data.put("templateName", record.getSourceName());
        data.put("templateCover", record.getSourceCover());
        data.put("printWidth", record.getPrintWidth());
        data.put("printHeight", record.getPrintHeight());
        data.put("printSizeString", record.getPrintSizeString());
        data.put("printCopies", record.getPrintCopies());
        data.put("printPlatform", record.getPrintPlatform());
        data.put("printPlatformName", record.getPrintPlatformName());
        data.put("printTime", record.getPrintTime());
        data.put("printStatus", record.getPrintStatus());
        data.put("printStatusName", record.getPrintStatusName());
        data.put("sourceData", record.getSourceData());
        data.put("createTime", record.getCreateTime());
        data.put("updateTime", record.getUpdateTime());
        return data;
    }
}
