package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseUserPrintSetting<M extends BaseUserPrintSetting<M>> extends Model<M> implements IBean {

	public M setUserId(java.lang.Integer userId) {
		set("user_id", userId);
		return (M)this;
	}
	
	public java.lang.Integer getUserId() {
		return getInt("user_id");
	}

	public M setRecordEnabled(java.lang.Boolean recordEnabled) {
		set("record_enabled", recordEnabled);
		return (M)this;
	}
	
	public java.lang.Boolean getRecordEnabled() {
		return getBool("record_enabled");
	}

	public M setUpdatedAt(java.util.Date updatedAt) {
		set("updated_at", updatedAt);
		return (M)this;
	}
	
	public java.util.Date getUpdatedAt() {
		return get("updated_at");
	}

}
