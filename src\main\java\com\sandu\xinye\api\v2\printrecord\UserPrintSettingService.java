package com.sandu.xinye.api.v2.printrecord;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.ehcache.CacheKit;
import com.sandu.xinye.api.v2.printrecord.exception.PrintRecordException;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.UserPrintSetting;

import java.sql.SQLException;
import java.util.Date;

/**
 * 用户打印记录设置服务类
 * 职责：管理用户打印记录开关设置，协调缓存和数据库操作
 *
 * <AUTHOR> Team
 */
public class UserPrintSettingService {
    public static final UserPrintSettingService me = new UserPrintSettingService();

    // 使用 EhCache 作为缓存，按照现有模式使用常量
    private static String cacheKey(Integer userId) { return "userPrintSetting:" + userId; }

    /**
     * 检查用户是否开启打印记录（带缓存优化）
     *
     * @param userId 用户ID
     * @return true-开启记录，false-关闭记录
     */
    public boolean isRecordEnabled(Integer userId) {
        if (userId == null) {
            return UserPrintSetting.RECORD_ENABLED; // 默认开启
        }

        // 使用 EhCache 缓存减少数据库查询
        Boolean cached = CacheKit.get(CacheConstant.USER_PRINT_SETTING, cacheKey(userId));
        if (cached != null) {
            return cached;
        }

        boolean enabled = UserPrintSetting.isUserRecordEnabled(userId);
        CacheKit.put(CacheConstant.USER_PRINT_SETTING, cacheKey(userId), enabled);
        return enabled;
    }

    /**
     * 设置用户打印记录开关
     * 
     * @param userId 用户ID
     * @param enabled 是否开启记录
     * @return 操作结果
     */
    public RetKit setRecordEnabled(Integer userId, boolean enabled) {
        // 修复：参数验证
        if (userId == null) {
            return RetKit.fail("用户ID不能为空");
        }
        if (userId <= 0) {
            return RetKit.fail("用户ID无效");
        }

        try {
            // 使用事务确保数据一致性 - 修复：将清空记录也放入事务
            boolean success = Db.tx(() -> {
                // 查找或创建用户设置
                UserPrintSetting setting = UserPrintSetting.findByUserId(userId);
                boolean settingSuccess;

                if (setting == null) {
                    // 创建新设置
                    setting = new UserPrintSetting();
                    setting.setUserId(userId);
                    setting.setRecordEnabled(enabled);
                    setting.setUpdatedAt(new Date());
                    settingSuccess = setting.save();
                } else {
                    // 更新现有设置
                    setting.setRecordEnabled(enabled);
                    setting.setUpdatedAt(new Date());
                    settingSuccess = setting.update();
                }

                // 如果设置成功且是关闭记录，在同一事务中清空记录
                if (settingSuccess && !enabled) {
                    int affectedRows = Db.update(
                        "UPDATE print_record SET delete_time = NOW() WHERE user_id = ? AND delete_time IS NULL",
                        userId
                    );
                    LogKit.info("事务中清空用户 " + userId + " 的打印记录，影响行数: " + affectedRows);
                }

                return settingSuccess;
            });

            if (success) {
                // 更新 EhCache 缓存
                CacheKit.put(CacheConstant.USER_PRINT_SETTING, cacheKey(userId), enabled);
                LogKit.info("用户 " + userId + " 设置打印记录开关为: " + enabled);
                return RetKit.ok();
            } else {
                return RetKit.fail("设置失败");
            }
        } catch (Exception e) {
            LogKit.error("设置用户打印记录开关失败 - 系统错误 - userId: " + userId + ", enabled: " + enabled, e);
            // 修复：不向前端泄露异常详情，区分异常类型
            return RetKit.fail("系统繁忙，请稍后重试");
        }
    }

    /**
     * 清空用户的打印记录（软删除）
     * 
     * @param userId 用户ID
     * @return 操作结果
     */
    public RetKit clearUserPrintRecords(Integer userId) {
        if (userId == null) {
            return RetKit.fail("用户ID不能为空");
        }

        try {
            // 软删除该用户的所有打印记录
            int affectedRows = Db.update(
                "UPDATE print_record SET delete_time = NOW() WHERE user_id = ? AND delete_time IS NULL",
                userId
            );
            
            LogKit.info("清空用户 " + userId + " 的打印记录，影响行数: " + affectedRows);
            return RetKit.ok("已清空 " + affectedRows + " 条打印记录");
        } catch (Exception e) {
            LogKit.error("清空用户打印记录失败 - userId: " + userId, e);
            return RetKit.fail("清空失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户打印记录设置
     * 
     * @param userId 用户ID
     * @return 设置信息
     */
    public RetKit getUserPrintSetting(Integer userId) {
        if (userId == null) {
            return RetKit.fail("用户ID不能为空");
        }

        try {
            boolean enabled = isRecordEnabled(userId);
            RetKit result = RetKit.ok();
            result.put("enabled", enabled);
            return result;
        } catch (Exception e) {
            LogKit.error("获取用户打印记录设置失败 - userId: " + userId, e);
            // 修复：不向前端泄露异常详情
            return RetKit.fail("系统繁忙，请稍后重试");
        }
    }
}
