-- XPrinter打印记录功能 - 索引优化脚本
-- 创建时间: 2024-08-27
-- 说明: 为打印记录搜索功能优化数据库索引，提升OR查询性能

USE xinye_app_v3;

-- =====================================================
-- 分析当前查询模式
-- =====================================================

-- 主要查询模式：
-- 1. 基础查询：WHERE user_id = ? AND print_type = ? AND delete_time IS NULL ORDER BY print_time DESC
-- 2. 搜索查询：上述条件 + (source_name LIKE '%keyword%' OR print_width = ? OR print_height = ?)

-- =====================================================
-- 索引优化策略
-- =====================================================

-- 策略1：为OR查询的每个条件创建复合索引
-- 这样MySQL可以使用index_merge优化，分别使用不同索引然后合并结果

-- 1. 优化source_name LIKE查询的索引（已存在，但可以优化）
-- 当前：KEY `idx_source_name` (`source_name`(100))
-- 优化：添加复合索引，包含常用过滤条件
DROP INDEX IF EXISTS idx_source_name ON print_record;
CREATE INDEX idx_search_by_name ON print_record (user_id, print_type, delete_time, source_name(100), print_time);

-- 2. 为print_width搜索创建复合索引
CREATE INDEX idx_search_by_width ON print_record (user_id, print_type, delete_time, print_width, print_time);

-- 3. 为print_height搜索创建复合索引  
CREATE INDEX idx_search_by_height ON print_record (user_id, print_type, delete_time, print_height, print_time);

-- 4. 保持原有的基础查询索引（列表查询）
-- idx_user_type_time (user_id, print_type, print_time) - 已存在，保持不变

-- =====================================================
-- 验证索引效果
-- =====================================================

-- 查看所有索引
SHOW INDEX FROM print_record;

-- 分析查询执行计划（需要有数据时测试）
-- EXPLAIN SELECT * FROM print_record 
-- WHERE user_id = 1 AND print_type = 1 AND delete_time IS NULL
-- AND (source_name LIKE '%商品%' OR print_width = 50 OR print_height = 30)
-- ORDER BY print_time DESC LIMIT 20;

-- =====================================================
-- 索引使用说明
-- =====================================================

/*
索引设计原理：

1. idx_search_by_name: 
   - 用于 source_name LIKE 查询
   - 包含 (user_id, print_type, delete_time, source_name, print_time)
   - 前缀索引source_name(100)平衡存储空间和查询效率

2. idx_search_by_width:
   - 用于 print_width = ? 查询  
   - 包含 (user_id, print_type, delete_time, print_width, print_time)
   - 支持精确匹配和排序

3. idx_search_by_height:
   - 用于 print_height = ? 查询
   - 包含 (user_id, print_type, delete_time, print_height, print_time)  
   - 支持精确匹配和排序

4. MySQL会使用index_merge策略：
   - 分别使用不同索引查询OR条件的各个分支
   - 合并结果集并去重
   - 最后按print_time排序

性能预期：
- 基础列表查询：使用 idx_user_type_time，性能保持不变
- 名称搜索：使用 idx_search_by_name，LIKE查询性能提升
- 尺寸搜索：使用 idx_search_by_width/height，精确匹配性能优秀
- 组合搜索：使用index_merge，整体性能显著提升

注意事项：
- 索引会增加写入开销，但对于查询为主的场景是值得的
- 定期监控索引使用情况，可通过 SHOW INDEX FROM print_record 查看
- 如果数据量很大，建议分批创建索引以避免锁表
*/

-- =====================================================
-- 可选：创建索引时的性能考虑
-- =====================================================

-- 如果表数据量很大（>100万行），建议分批创建索引：
-- 1. 在业务低峰期执行
-- 2. 一次创建一个索引
-- 3. 监控服务器负载

-- 示例：分批创建（取消注释使用）
-- CREATE INDEX idx_search_by_name ON print_record (user_id, print_type, delete_time, source_name(100), print_time);
-- -- 等待几分钟，确认系统负载正常
-- CREATE INDEX idx_search_by_width ON print_record (user_id, print_type, delete_time, print_width, print_time);
-- -- 等待几分钟，确认系统负载正常  
-- CREATE INDEX idx_search_by_height ON print_record (user_id, print_type, delete_time, print_height, print_time);
