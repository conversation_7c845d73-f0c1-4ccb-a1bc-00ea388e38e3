<?xml version="1.0" encoding="UTF-8"?>
<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="ehcache.xsd" updateCheck="false"
	monitoring="autodetect" dynamicConfig="true">

	<diskStore path="java.io.tmpdir" />

	<defaultCache maxEntriesLocalHeap="10000" eternal="false"
		overflowToDisk="true" timeToIdleSeconds="20" timeToLiveSeconds="60">
	</defaultCache>

	<!-- 缓存登录用户信息，空闲 30 分钟则清除，否则一直保持登录状态， 登录过期时间是指 session表中的 expireAt，此缓存仅用于统计
		login_log 以及提升性能(不必总读取数据库 session 表) -->

	<cache name="SYS_USER" maxEntriesLocalHeap="90000" eternal="false"
		timeToIdleSeconds="1800" timeToLiveSeconds="0" overflowToDisk="false" />


	<cache name="SYS_ROLE_MENU" maxEntriesLocalHeap="200" eternal="false"
		timeToIdleSeconds="1800" timeToLiveSeconds="0" overflowToDisk="false" />

	<!-- 验证码缓存 -->
	<cache name="CAPTCHA" maxEntriesLocalHeap="90000"
		eternal="false" timeToIdleSeconds="300" timeToLiveSeconds="300"
		overflowToDisk="false" />

	<!-- 1天无访问过期 -->
	<cache name="APP_USER" maxEntriesLocalHeap="90000" eternal="false"
	timeToIdleSeconds="86400" timeToLiveSeconds="0" overflowToDisk="false" />

	<!-- 模板分享缓存 -->
	<cache name="TEMPLET_SHARE" maxEntriesLocalHeap="90000" eternal="false"
		   timeToIdleSeconds="86400" timeToLiveSeconds="86400" />

	<!-- 用户打印记录设置缓存 -->
	<cache name="USER_PRINT_SETTING" maxEntriesLocalHeap="50000" eternal="false"
		   timeToIdleSeconds="86400" timeToLiveSeconds="0" overflowToDisk="false" />

	<!-- 接口访问次数限制 -->
	<!-- <cache name="INTERFACE_COUNT" maxEntriesLocalHeap="90000"
		eternal="false"  timeToLiveSeconds="60"
		overflowToDisk="false" /> -->


	<!-- name：Cache的名称，必须是唯一的(ehcache会把这个cache放到HashMap里)。 maxElementsInMemory：内存中保持的对象数量。
		maxElementsOnDisk：DiskStore中保持的对象数量，默认值为0，表示不限制。 eternal：是否是永恒数据，如果是，则它的超时设置会被忽略。
		overflowToDisk：如果内存中数据数量超过maxElementsInMemory限制，是否要缓存到磁盘上。 timeToIdleSeconds：对象空闲时间，指对象在多长时间没有被访问就会失效。只对eternal为false的有效。默认值0，表示一直可以访问。
		timeToLiveSeconds：对象存活时间，指对象从创建到失效所需要的时间。只对eternal为false的有效。默认值0，表示一直可以访问。
		diskPersistent：是否在磁盘上持久化。指重启jvm后，数据是否有效。默认为false。 diskExpiryThreadIntervalSeconds：对象检测线程运行时间间隔。标识对象状态的线程多长时间运行一次。
		diskSpoolBufferSizeMB：DiskStore使用的磁盘大小，默认值30MB。每个cache使用各自的DiskStore。 memoryStoreEvictionPolicy：如果内存中数据超过内存限制，向磁盘缓存时的策略。默认值LRU，可选FIFO、LFU。 -->
</ehcache>
